provider "aws" {
  region = var.region
}

terraform {
  required_version = ">= 1.5.1"

  backend "s3" {
    bucket         = "gus-eip-infra-terraform-state-lock-dev"
    key            = "terraform.tfstate"
    region         = "eu-west-1"
    dynamodb_table = "gus-eip-infra-terraform-state-lock-dev"
    encrypt        = true
  }
}

module "iam" {
  environment_tag                      = var.environment_tag
  source                               = "./modules/iam"
  region                               = var.region
  accountId                            = var.accountId
  environment                          = var.environment
  athena_access_accountId              = var.athena_access_accountId
  athena_access_role_name              = var.athena_access_role_name
  r3_oaf_frontend_pipeline_name        = var.r3_oaf_frontend_pipeline_name
  gus_middleware_service_pipeline_name = var.gus_middleware_service_pipeline_name
  r3_pdf_generator_pipeline_name       = var.r3_pdf_generator_pipeline_name
  r3_oaf_backend_pipeline_name         = var.r3_oaf_backend_pipeline_name
  s3_role_arn                          = var.s3_role_arn
  prod_lambda_assumed_role_arn         = var.prod_lambda_assumed_role_arn
  prod_oap_lambda_assumed_role_arn     = var.prod_oap_lambda_assumed_role_arn
  dev_lambda_assumed_role_arn          = var.dev_lambda_assumed_role_arn
  dev_ecs_assumed_role_arn             = var.dev_ecs_assumed_role_arn
  s3_oap_cross_account_role_arn        = var.s3_oap_cross_account_role_arn
}

module "lambda" {
  environment_tag                                      = var.environment_tag
  source                                               = "./modules/lambda"
  region                                               = var.region
  environment                                          = var.environment
  accountId                                            = var.accountId
  xray_traces_bucket_name                              = var.xray_traces_bucket_name
  xray_data_s3_folder                                  = var.xray_data_s3_folder
  api_id                                               = var.api_id
  consumer_config_table                                = var.consumer_config_table
  api_xraytraces_function_name                         = var.api_xraytraces_function_name
  cloudwatch_event_rule_lambda_schedule_name           = var.cloudwatch_event_rule_lambda_schedule_name
  cloudwatch_event_rule_schedule_expression            = var.cloudwatch_event_rule_schedule_expression
  cloudwatch_event_rule_schedule_xray_lambda_target_id = var.cloudwatch_event_rule_schedule_xray_lambda_target_id
  r3_oaf_service_api_id                                = var.r3_oaf_service_api_id
  apphero_api_id                                       = var.apphero_api_id
  eip_service_api_id                                   = var.eip_service_api_id
  student_details_api_id                               = var.student_details_api_id
}

module "r3_apigateway" {
  source                                      = "./modules/r3apigateway"
  region                                      = var.region
  environment                                 = var.environment
  accountId                                   = var.accountId
  development_usage_plan_name                 = var.development_usage_plan_name
  cognito_user_pool_id                        = var.cognito_user_pool_id
  environment_tag                             = var.environment_tag
  r3_oaf_gateway_custom_domain                = var.r3_oaf_gateway_custom_domain
  api_gateway_certificate_acm_certificate_arn = var.api_gateway_certificate_acm_certificate_arn
}

module "eip_apigateway" {
  source                                      = "./modules/guseipapigateway"
  region                                      = var.region
  environment                                 = var.environment
  accountId                                   = var.accountId
  development_usage_plan_name                 = var.development_usage_plan_name
  cognito_user_pool_id                        = var.cognito_user_pool_id
  environment_tag                             = var.environment_tag
  api_gateway_certificate_acm_certificate_arn = var.api_gateway_certificate_acm_certificate_arn
  oap_integration_consumer_api_key            = var.oap_integration_consumer_api_key
  apphero_consumer_api_key                    = var.apphero_consumer_api_key
  ibat_el_consumer_api_key                    = var.ibat_el_consumer_api_key
  lim_consumer_api_key                        = var.lim_consumer_api_key
  hzu_consumer_api_key                        = var.hzu_consumer_api_key
  unfc_consumer_api_key                       = var.unfc_consumer_api_key
  r3_consumer_api_key                         = var.r3_consumer_api_key
  eip_gateway_certificate_acm_certificate_arn = var.eip_gateway_certificate_acm_certificate_arn
  eip_gateway_custom_domain                   = var.eip_gateway_custom_domain
  ibat_pipeline_consumer_api_key              = var.ibat_pipeline_consumer_api_key
  ibat_pipeline_cognito_user_pool_id          = var.ibat_pipeline_cognito_user_pool_id
  gus_eip_sf_api_key                          = var.gus_eip_sf_api_key
  gus_universal_api_key                       = var.gus_universal_api_key
  ue_consumer_api_key                         = var.ue_consumer_api_key
  ard_consumer_api_key                        = var.ard_consumer_api_key
  wul_consumer_api_key                        = var.wul_consumer_api_key
  logs_summery_cognito_user_pool_id           = var.logs_summery_cognito_user_pool_id
}
module "apigateway" {
  source                                      = "./modules/apigateway"
  region                                      = var.region
  environment                                 = var.environment
  accountId                                   = var.accountId
  development_usage_plan_name                 = var.development_usage_plan_name
  cognito_user_pool_id                        = var.cognito_user_pool_id
  environment_tag                             = var.environment_tag
  api_gateway_certificate_acm_certificate_arn = var.api_gateway_certificate_acm_certificate_arn
}

module "s3" {
  environment_tag                     = var.environment_tag
  source                              = "./modules/s3"
  region                              = var.region
  environment                         = var.environment
  gus_xray_traces_bucket_name         = var.gus_xray_traces_bucket_name
  gus_athena_query_output_bucket_name = var.gus_athena_query_output_bucket_name
  r3_oaf_frontend_bucket_name         = var.r3_oaf_frontend_bucket_name
  r3_oaf_backend_bucket_name          = var.r3_oaf_backend_bucket_name
  gus_middleware_service_bucket_name  = var.gus_middleware_service_bucket_name
  r3_pdf_generator_bucket_name        = var.r3_pdf_generator_bucket_name
  gus_eip_analytics_bucket_name       = var.gus_eip_analytics_bucket_name
  gus_eip_log_processor_bucket_name   = var.gus_eip_log_processor_bucket_name
}

module "glue" {
  environment_tag                   = var.environment_tag
  source                            = "./modules/glue"
  region                            = var.region
  environment                       = var.environment
  xray_traces_catalog_database_name = var.xray_traces_catalog_database_name
  xray_traces_catalog_table_name    = var.xray_traces_catalog_table_name
  aws_glue_catalog_table_type       = var.aws_glue_catalog_table_type
  storage_descriptor_location       = var.storage_descriptor_location
  storage_descriptor_input_format   = var.storage_descriptor_input_format
  storage_descriptor_output_format  = var.storage_descriptor_output_format
  ser_de_info_name                  = var.ser_de_info_name
  ser_de_info_serialization_library = var.ser_de_info_serialization_library
}

module "codebuild" {
  source                                                                 = "./modules/codebuild"
  region                                                                 = var.region
  environment                                                            = var.environment
  accountId                                                              = var.accountId
  environment_tag                                                        = var.environment_tag
  r3_oaf_frontend_project_name                                           = var.r3_oaf_frontend_project_name
  r3_oaf_frontend_project_build_timeout                                  = var.r3_oaf_frontend_project_build_timeout
  r3_oaf_frontend_project_source_type                                    = var.r3_oaf_frontend_project_source_type
  r3_oaf_frontend_project_environment_compute_type                       = var.r3_oaf_frontend_project_environment_compute_type
  r3_oaf_frontend_project_environment_image                              = var.r3_oaf_frontend_project_environment_image
  r3_oaf_frontend_project_environment_type                               = var.r3_oaf_frontend_project_environment_type
  r3_oaf_frontend_project_environment_image_pull_credentials_type        = var.r3_oaf_frontend_project_environment_image_pull_credentials_type
  r3_oaf_frontend_project_artifact_type                                  = var.r3_oaf_frontend_project_artifact_type
  r3_oaf_backend_project_name                                            = var.r3_oaf_backend_project_name
  r3_oaf_backend_project_build_timeout                                   = var.r3_oaf_backend_project_build_timeout
  r3_oaf_backend_project_source_type                                     = var.r3_oaf_backend_project_source_type
  r3_oaf_backend_project_environment_compute_type                        = var.r3_oaf_backend_project_environment_compute_type
  r3_oaf_backend_project_environment_image                               = var.r3_oaf_backend_project_environment_image
  r3_oaf_backend_project_environment_type                                = var.r3_oaf_backend_project_environment_type
  r3_oaf_backend_project_environment_image_pull_credentials_type         = var.r3_oaf_backend_project_environment_image_pull_credentials_type
  r3_oaf_backend_project_artifact_type                                   = var.r3_oaf_backend_project_artifact_type
  gus_middleware_service_project_name                                    = var.gus_middleware_service_project_name
  gus_middleware_service_project_build_timeout                           = var.gus_middleware_service_project_build_timeout
  gus_middleware_service_project_source_type                             = var.gus_middleware_service_project_source_type
  gus_middleware_service_project_environment_compute_type                = var.gus_middleware_service_project_environment_compute_type
  gus_middleware_service_project_environment_image                       = var.gus_middleware_service_project_environment_image
  gus_middleware_service_project_environment_type                        = var.gus_middleware_service_project_environment_type
  gus_middleware_service_project_environment_image_pull_credentials_type = var.gus_middleware_service_project_environment_image_pull_credentials_type
  gus_middleware_service_project_artifact_type                           = var.gus_middleware_service_project_artifact_type
  r3_pdf_generator_project_name                                          = var.r3_pdf_generator_project_name
  r3_pdf_generator_project_build_timeout                                 = var.r3_pdf_generator_project_build_timeout
  r3_pdf_generator_project_source_type                                   = var.r3_pdf_generator_project_source_type
  r3_pdf_generator_project_environment_compute_type                      = var.r3_pdf_generator_project_environment_compute_type
  r3_pdf_generator_project_environment_image                             = var.r3_pdf_generator_project_environment_image
  r3_pdf_generator_project_environment_type                              = var.r3_pdf_generator_project_environment_type
  r3_pdf_generator_project_environment_image_pull_credentials_type       = var.r3_pdf_generator_project_environment_image_pull_credentials_type
  r3_pdf_generator_project_artifact_type                                 = var.r3_pdf_generator_project_artifact_type
  gus_eip_analytics_project_name                                         = var.gus_eip_analytics_project_name
  gus_eip_analytics_project_build_timeout                                = var.gus_eip_analytics_project_build_timeout
  gus_eip_analytics_project_source_type                                  = var.gus_eip_analytics_project_source_type
  gus_eip_analytics_project_environment_compute_type                     = var.gus_eip_analytics_project_environment_compute_type
  gus_eip_analytics_project_environment_image                            = var.gus_eip_analytics_project_environment_image
  gus_eip_analytics_project_environment_type                             = var.gus_eip_analytics_project_environment_type
  gus_eip_analytics_project_environment_image_pull_credentials_type      = var.gus_eip_analytics_project_environment_image_pull_credentials_type
  gus_eip_analytics_project_artifact_type                                = var.gus_eip_analytics_project_artifact_type
  restApiId                                                              = var.restApiId
  restApiRootResourceId                                                  = var.restApiRootResourceId
  securityGroupId                                                        = var.securityGroupId
  subnetId                                                               = var.subnetId
  gus_eip_log_processor_project_name                                     = var.gus_eip_log_processor_project_name
  gus_eip_log_processor_project_build_timeout                            = var.gus_eip_log_processor_project_build_timeout
  gus_eip_log_processor_project_source_type                              = var.gus_eip_log_processor_project_source_type
  gus_eip_log_processor_project_environment_compute_type                 = var.gus_eip_log_processor_project_environment_compute_type
  gus_eip_log_processor_project_environment_image                        = var.gus_eip_log_processor_project_environment_image
  gus_eip_log_processor_project_environment_type                         = var.gus_eip_log_processor_project_environment_type
  gus_eip_log_processor_project_environment_image_pull_credentials_type  = var.gus_eip_log_processor_project_environment_image_pull_credentials_type
  gus_eip_log_processor_project_artifact_type                            = var.gus_eip_log_processor_project_artifact_type
  gus_eip_logger_frontend_project_name                                         = var.gus_eip_logger_frontend_project_name
  gus_eip_logger_frontend_project_build_timeout                                = var.gus_eip_logger_frontend_project_build_timeout
  gus_eip_logger_frontend_project_source_type                                  = var.gus_eip_logger_frontend_project_source_type
  gus_eip_logger_frontend_project_environment_compute_type                     = var.gus_eip_logger_frontend_project_environment_compute_type
  gus_eip_logger_frontend_project_environment_image                            = var.gus_eip_logger_frontend_project_environment_image
  gus_eip_logger_frontend_project_environment_type                             = var.gus_eip_logger_frontend_project_environment_type
  gus_eip_logger_frontend_project_environment_image_pull_credentials_type      = var.gus_eip_logger_frontend_project_environment_image_pull_credentials_type
  gus_eip_logger_frontend_project_artifact_type                                = var.gus_eip_logger_frontend_project_artifact_type
}

module "codepipeline" {
  environment_tag                                               = var.environment_tag
  source                                                        = "./modules/codepipeline"
  region                                                        = var.region
  environment                                                   = var.environment
  accountId                                                     = var.accountId
  r3_oaf_frontend_pipeline_name                                 = var.r3_oaf_frontend_pipeline_name
  r3_oaf_frontend_pipeline_artifact_store_location              = var.r3_oaf_frontend_pipeline_artifact_store_location
  r3_oaf_frontend_pipeline_artifact_store_type                  = var.r3_oaf_frontend_pipeline_artifact_store_type
  r3_oaf_frontend_pipeline_source_config_repository_name        = var.r3_oaf_frontend_pipeline_source_config_repository_name
  r3_oaf_frontend_pipeline_source_config_branch_name            = var.r3_oaf_frontend_pipeline_source_config_branch_name
  r3_oaf_frontend_pipeline_project_name                         = var.r3_oaf_frontend_pipeline_project_name
  r3_oaf_backend_pipeline_name                                  = var.r3_oaf_backend_pipeline_name
  r3_oaf_backend_pipeline_artifact_store_location               = var.r3_oaf_backend_pipeline_artifact_store_location
  r3_oaf_backend_pipeline_artifact_store_type                   = var.r3_oaf_backend_pipeline_artifact_store_type
  r3_oaf_backend_pipeline_source_config_repository_name         = var.r3_oaf_backend_pipeline_source_config_repository_name
  r3_oaf_backend_pipeline_source_config_branch_name             = var.r3_oaf_backend_pipeline_source_config_branch_name
  r3_oaf_backend_pipeline_project_name                          = var.r3_oaf_backend_pipeline_project_name
  gus_middleware_service_pipeline_name                          = var.gus_middleware_service_pipeline_name
  gus_middleware_service_pipeline_artifact_store_location       = var.gus_middleware_service_pipeline_artifact_store_location
  gus_middleware_service_pipeline_artifact_store_type           = var.gus_middleware_service_pipeline_artifact_store_type
  gus_middleware_service_pipeline_source_config_repository_name = var.gus_middleware_service_pipeline_source_config_repository_name
  gus_middleware_service_pipeline_source_config_branch_name     = var.gus_middleware_service_pipeline_source_config_branch_name
  gus_middleware_service_pipeline_project_name                  = var.gus_middleware_service_pipeline_project_name
  r3_pdf_generator_pipeline_name                                = var.r3_pdf_generator_pipeline_name
  r3_pdf_generator_pipeline_artifact_store_location             = var.r3_pdf_generator_pipeline_artifact_store_location
  r3_pdf_generator_pipeline_artifact_store_type                 = var.r3_pdf_generator_pipeline_artifact_store_type
  r3_pdf_generator_pipeline_source_config_repository_name       = var.r3_pdf_generator_pipeline_source_config_repository_name
  r3_pdf_generator_pipeline_source_config_branch_name           = var.r3_pdf_generator_pipeline_source_config_branch_name
  r3_pdf_generator_pipeline_project_name                        = var.r3_pdf_generator_pipeline_project_name
  gus_eip_analytics_pipeline_name                                = var.gus_eip_analytics_pipeline_name
  gus_eip_analytics_pipeline_artifact_store_location             = var.gus_eip_analytics_pipeline_artifact_store_location
  gus_eip_analytics_pipeline_artifact_store_type                 = var.gus_eip_analytics_pipeline_artifact_store_type
  gus_eip_analytics_pipeline_source_config_repository_name       = var.gus_eip_analytics_pipeline_source_config_repository_name
  gus_eip_analytics_pipeline_source_config_branch_name           = var.gus_eip_analytics_pipeline_source_config_branch_name
  gus_eip_analytics_pipeline_project_name                        = var.gus_eip_analytics_pipeline_project_name
  gus_eip_log_processor_pipeline_name                            = var.gus_eip_log_processor_pipeline_name
  gus_eip_log_processor_pipeline_artifact_store_location         = var.gus_eip_log_processor_pipeline_artifact_store_location
  gus_eip_log_processor_pipeline_artifact_store_type             = var.gus_eip_log_processor_pipeline_artifact_store_type
  gus_eip_log_processor_pipeline_source_config_repository_name   = var.gus_eip_log_processor_pipeline_source_config_repository_name
  gus_eip_log_processor_pipeline_source_config_branch_name       = var.gus_eip_log_processor_pipeline_source_config_branch_name
  gus_eip_log_processor_pipeline_project_name                    = var.gus_eip_log_processor_pipeline_project_name
  gus_eip_logger_frontend_pipeline_name                                = var.gus_eip_logger_frontend_pipeline_name
  gus_eip_logger_frontend_pipeline_artifact_store_location             = var.gus_eip_logger_frontend_pipeline_artifact_store_location
  gus_eip_logger_frontend_pipeline_artifact_store_type                 = var.gus_eip_logger_frontend_pipeline_artifact_store_type
  gus_eip_logger_frontend_pipeline_source_config_repository_name       = var.gus_eip_logger_frontend_pipeline_source_config_repository_name
  gus_eip_logger_frontend_pipeline_source_config_branch_name           = var.gus_eip_logger_frontend_pipeline_source_config_branch_name
  gus_eip_logger_frontend_pipeline_project_name                        = var.gus_eip_logger_frontend_pipeline_project_name
}

module "cloudfront" {
  source                                                            = "./modules/cloudfront"
  region                                                            = var.region
  environment                                                       = var.environment
  s3_distribution_r3_content_security_policy                        = var.s3_distribution_r3_content_security_policy
  s3_distribution_domain_name                                       = var.s3_distribution_domain_name
  s3_distribution_origin_id                                         = var.s3_distribution_origin_id
  s3_distribution_origin_protocol_policy                            = var.s3_distribution_origin_protocol_policy
  s3_distribution_origin_ssl_protocols                              = var.s3_distribution_origin_ssl_protocols
  s3_distribution_http_port                                         = var.s3_distribution_http_port
  s3_distribution_https_port                                        = var.s3_distribution_https_port
  s3_distribution_enabled                                           = var.s3_distribution_enabled
  gus_alternative_domain                                            = var.gus_alternative_domain
  s3_distribution_is_ipv6_enabled                                   = var.s3_distribution_is_ipv6_enabled
  s3_distribution_default_cache_behavior_allowed_methods            = var.s3_distribution_default_cache_behavior_allowed_methods
  s3_distribution_default_cache_behavior_cached_methods             = var.s3_distribution_default_cache_behavior_cached_methods
  s3_distribution_default_cache_behavior_target_origin_id           = var.s3_distribution_default_cache_behavior_target_origin_id
  s3_distribution_viewer_protocol_policy                            = var.s3_distribution_viewer_protocol_policy
  s3_distribution_geo_restriction_restriction_type                  = var.s3_distribution_geo_restriction_restriction_type
  s3_distribution_viewer_certificate_cloudfront_default_certificate = var.s3_distribution_viewer_certificate_cloudfront_default_certificate
  s3_distribution_viewer_certificate_acm_certificate_arn            = var.s3_distribution_viewer_certificate_acm_certificate_arn
  s3_distribution_default_cache_behavior_cache_policy_id            = var.s3_distribution_default_cache_behavior_cache_policy_id
  s3_distribution_default_cache_behavior_response_headers_policy_id = var.s3_distribution_default_cache_behavior_response_headers_policy_id
  environment_tag                                                   = var.environment_tag
}

module "cognito" {
  environment_tag                             = var.environment_tag
  source                                      = "./modules/cognito"
  region                                      = var.region
  environment                                 = var.environment
  accountId                                   = var.accountId
  api_gateway_certificate_acm_certificate_arn = var.api_gateway_certificate_acm_certificate_arn
  cognito_ibd_custom_domain                   = var.cognito_ibd_custom_domain
}

module "ecs-cdc-service" {
  source      = "./modules/ecs-cdc-service"
  region      = var.region
  environment = var.environment
  accountId   = var.accountId
}

module "sqs" {
  source = "./modules/sqs"
  environment_tag = var.environment_tag
  log_processing_queue_name = var.log_processing_queue_name
  region = var.region
  environment = var.environment
}
