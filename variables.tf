variable "region" {
  description = ""
  type        = string
}

variable "environment" {
  description = ""
  type        = string
}

variable "accountId" {
  description = ""
  type        = string
}

variable "environment_tag" {
  description = "Environment"
  type        = string
}

#apigateway

variable "eip_gateway_certificate_acm_certificate_arn" {
  type = string
}

variable "ibat_pipeline_consumer_api_key" {
  type = string
}

variable "eip_gateway_custom_domain" {
  type = string
}

variable "development_usage_plan_name" {
  description = ""
  type        = string
}

variable "cognito_user_pool_id" {
  description = ""
  type        = string
}

variable "r3_oaf_gateway_custom_domain" {
  type = string
}

variable "api_gateway_certificate_acm_certificate_arn" {
  type = string
}

variable "r3_consumer_api_key" {
  type = string
}

variable "ard_consumer_api_key" {
  type = string
}

variable "wul_consumer_api_key" {
  type = string
}

variable "apphero_consumer_api_key" {
  type = string
}

variable "ibat_el_consumer_api_key" {
  type = string
}

variable "lim_consumer_api_key" {
  type = string
}

variable "hzu_consumer_api_key" {
  type = string
}

variable "unfc_consumer_api_key" {
  type = string
}

variable "ue_consumer_api_key"{
  type = string
}

variable "gus_eip_sf_api_key"{
    type = string
}

variable "oap_integration_consumer_api_key" {
  type = string
}

variable "gus_universal_api_key" {
  type = string
}

#s3
variable "gus_xray_traces_bucket_name" {
  type = string
}

variable "gus_athena_query_output_bucket_name" {
  type = string
}

variable "r3_oaf_frontend_bucket_name" {
  type = string
}

variable "r3_oaf_backend_bucket_name" {
  type = string
}

variable "gus_middleware_service_bucket_name" {
  type = string
}

variable "r3_pdf_generator_bucket_name" {
  type = string
}

variable "gus_eip_analytics_bucket_name" {
  type = string
}

variable "gus_eip_log_processor_bucket_name" {
  type = string
  
}

#lambda

variable "xray_traces_bucket_name" {
  description = ""
  type        = string
}

variable "xray_data_s3_folder" {
  description = ""
  type        = string
}

variable "api_id" {
  description = ""
  type        = string
}

variable "r3_oaf_service_api_id" {
  description = ""
  type        = string
}

variable "eip_service_api_id" {
  description = ""
  type        = string
}

variable "student_details_api_id" {
  description = ""
  type        = string
}

variable "apphero_api_id" {
  description = ""
  type        = string
}

variable "consumer_config_table" {
  description = ""
  type        = string
}

variable "api_xraytraces_function_name" {
  description = ""
  type        = string
}


variable "cloudwatch_event_rule_lambda_schedule_name" {
  description = ""
  type        = string
}

variable "cloudwatch_event_rule_schedule_expression" {
  description = ""
  type        = string
}

variable "cloudwatch_event_rule_schedule_xray_lambda_target_id" {
  description = ""
  type        = string
}

#glue 

variable "xray_traces_catalog_database_name" {
  type = string
}


variable "xray_traces_catalog_table_name" {
  type = string
}

variable "aws_glue_catalog_table_type" {
  type = string
}

variable "storage_descriptor_location" {
  type = string
}

variable "storage_descriptor_input_format" {
  type = string
}

variable "storage_descriptor_output_format" {
  type = string
}

variable "ser_de_info_name" {
  type = string
}

variable "ser_de_info_serialization_library" {
  type = string
}

# athena 
# variable "xray_traces_query" {
#   description = ""
#   type        = string
# }


#codepipeline

variable "r3_oaf_frontend_pipeline_name" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_pipeline_project_name" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_pipeline_name" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_pipeline_project_name" {
  description = ""
  type        = string
}

variable "gus_middleware_service_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_middleware_service_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_middleware_service_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_middleware_service_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_middleware_service_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_middleware_service_pipeline_project_name" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_pipeline_name" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_pipeline_project_name" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_pipeline_project_name" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_pipeline_project_name" {
  description = ""
  type        = string
}

//gus-eip-logger-frontend
variable "gus_eip_logger_frontend_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_pipeline_project_name" {
  description = ""
  type        = string
}

#codebuild
variable "r3_oaf_frontend_project_name" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_build_timeout" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_source_type" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_environment_image" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_environment_type" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_artifact_type" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_name" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_build_timeout" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_source_type" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_environment_image" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_environment_type" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_artifact_type" {
  description = ""
  type        = string
}

 variable "restApiId" {
  description = ""
  type = string
}

variable "restApiRootResourceId" {
  description = ""
  type = string
  
}

variable "securityGroupId" {
  description = ""
  type = string
  
}

variable "subnetId" {
  description = ""
  type = string
  
}

//gus-middleware-service
variable "gus_middleware_service_project_name" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_source_type" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_artifact_type" {
  description = ""
  type        = string
}

//r3-pdf-generator 
variable "r3_pdf_generator_project_name" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_build_timeout" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_source_type" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_environment_image" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_environment_type" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_artifact_type" {
  description = ""
  type        = string
}

//gus-eip-analytics 
variable "gus_eip_analytics_project_name" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_source_type" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_artifact_type" {
  description = ""
  type        = string
}

//gus-eip-log-processor 
variable "gus_eip_log_processor_project_name" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_source_type" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_artifact_type" {
  description = ""
  type        = string
}

//gus-eip-logger-frontend
variable "gus_eip_logger_frontend_project_name" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_source_type" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_artifact_type" {
  description = ""
  type        = string
}

#cloudfront
variable "s3_distribution_domain_name" {
  description = ""
  type        = string
}

variable "s3_distribution_r3_content_security_policy" {
  description = ""
  type        = string
}

variable "s3_distribution_origin_id" {
  description = ""
  type        = string
}

variable "s3_distribution_origin_protocol_policy" {
  description = ""
  type        = string
}

variable "s3_distribution_origin_ssl_protocols" {
  description = ""
  type        = list(string)
}


variable "s3_distribution_http_port" {
  description = ""
  type        = number
}

variable "s3_distribution_https_port" {
  description = ""
  type        = number
}

variable "s3_distribution_enabled" {
  description = ""
  type        = bool
}

variable "s3_distribution_is_ipv6_enabled" {
  description = ""
  type        = bool
}

variable "gus_alternative_domain" {
  description = ""
  type        = list(string)
}


variable "s3_distribution_default_cache_behavior_allowed_methods" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_default_cache_behavior_cached_methods" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_default_cache_behavior_target_origin_id" {
  description = ""
  type        = string
}

variable "s3_distribution_viewer_protocol_policy" {
  description = ""
  type        = string
}

variable "s3_distribution_geo_restriction_restriction_type" {
  description = ""
  type        = string
}

variable "s3_distribution_viewer_certificate_cloudfront_default_certificate" {
  description = ""
  type        = bool
}

variable "s3_distribution_viewer_certificate_acm_certificate_arn" {
  description = ""
  type        = string
}

variable "s3_distribution_default_cache_behavior_cache_policy_id" {
  description = ""
  type        = string
}

variable "s3_distribution_default_cache_behavior_response_headers_policy_id" {
  description = ""
  type        = string
}

//iam

variable "athena_access_accountId" {
  description = ""
  type        = string
}

variable "athena_access_role_name" {
  description = ""
  type        = string
}

variable "s3_role_arn" {
  description = ""
  type        = string
}

variable "dev_lambda_assumed_role_arn" {
  description = ""
  type        = string
}

variable "prod_lambda_assumed_role_arn" {
  description = ""
  type        = string
}

variable "prod_oap_lambda_assumed_role_arn" {
  description = ""
  type        = string
}

variable "dev_ecs_assumed_role_arn" {
  description = ""
  type        = string
}

variable "ses_mailer" {
  description = ""
  type        = string
}

variable "ibat_pipeline_cognito_user_pool_id"{
  description = ""
  type        = string
}
//cognito
variable "cognito_ibd_custom_domain" {
  description = ""
  type        = string
}
variable "logs_summery_cognito_user_pool_id" {
  description = ""
  type = string
}
//iam
variable "s3_oap_cross_account_role_arn" {
  description = ""
  type        = string
}

//sqs
variable "log_processing_queue_name" {
    description = "log_processing_queue_name"
    type = string
}
