region          = "eu-west-1"
environment     = "dev"
accountId       = "************"
environment_tag = "DEV"

#apigateway
development_usage_plan_name                 = "Development"
cognito_user_pool_id                        = "eu-west-1_kdJiJ2JJp"
r3_oaf_gateway_custom_domain                = "gusmedical-dev-api.apphero.io"
eip_gateway_custom_domain                   = "dev-api.guseip.io"
api_gateway_certificate_acm_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/35116c27-4b9b-4a39-8cf5-34d7cd6b2f36"
r3_consumer_api_key                         = "4nO6iELjjIpiKyPjLuv5800weVG2a4u7VJxcgxc8"
apphero_consumer_api_key                    = "xphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ"
ibat_el_consumer_api_key                    = "yphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ"
lim_consumer_api_key                        = "yphESfRh2o5J7L87WsKfh2MIBWSdPHev4TNPSGNZ"
hzu_consumer_api_key                        = "yphESfRh2o5J7L87WsKfh2MIBWSdPHev5TNPSGNZ"
unfc_consumer_api_key                        = "yphESfRh2o5J7L87WsKfh2MIBWSdPHev6TNPSGNZ"
oap_integration_consumer_api_key            = "ve8fbhpkCJ34nu7aUMH3L2lAllizvYvb19t7pioO"
ibat_pipeline_consumer_api_key              = "ie8fbhpkCJ34nu8aUMH3L2lAllizvYvb19t7pioO"
gus_eip_sf_api_key                          = "n2fakdBcpM54gh1qUPJ9X3vAllztyXcb20t6nkqZ"
gus_universal_api_key                       = "6dGmdp0bVU94I2y9CT6KhgFoUyPQDeB8bniohUea"
ue_consumer_api_key                         = "zxTQGpLm9r2K5V34YpMfX8NOCJWdBHtv7RLDSAEK"
ard_consumer_api_key                        = "aVzNkXtQ5m9LdC72RyKhf8WoEJGbPLsu6TMWYnei"
wul_consumer_api_key                        = "wLxHpMzR7v3JcD94QyNfg8AsEVKbTXuo5AZWdNiq"
eip_gateway_certificate_acm_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/0951bbd6-cc90-46a6-a0bd-31743b129b37"
#s3
gus_xray_traces_bucket_name         = "gus-eip-xraytraces-data"
gus_athena_query_output_bucket_name = "gus-eip-athena-query-result"
r3_oaf_frontend_bucket_name         = "r3-oaf-frontend"
r3_oaf_backend_bucket_name          = "r3-oaf-backend"
gus_middleware_service_bucket_name  = "gus-middleware-service"
r3_pdf_generator_bucket_name        = "r3-pdf-generator"
gus_eip_analytics_bucket_name       = "gus-eip-analytics"
gus_eip_log_processor_bucket_name   = "gus-eip-log-processor"


#lambda 
ses_mailer                                           = "<EMAIL>"
xray_traces_bucket_name                              = "gus-eip-xraytraces-data-dev"
xray_data_s3_folder                                  = "xray_parquet_data"
api_id                                               = "ge8fq00yg4"
r3_oaf_service_api_id                                = "y6atqwjota"
apphero_api_id                                       = "bb5speltzk"
eip_service_api_id                                   = "ybafgiu3j6"
api_key                                              = "4nO6iELjjIpiKyPjLuv5800weVG2a4u7VJxcgxc9"
student_details_api_id                               = "5octmg0qce"
consumer_config_table                                = "gus-eip-consumer-dev"
api_xraytraces_function_name                         = "gus-eip-xraytraces-data"
cloudwatch_event_rule_lambda_schedule_name           = "gus-eip-xray-data-schedule"
cloudwatch_event_rule_schedule_expression            = "rate(5 minutes)"
cloudwatch_event_rule_schedule_xray_lambda_target_id = "gus-eip-xraytraces-data"

#glue 
xray_traces_catalog_database_name = "gus-eip-xray-traces"
xray_traces_catalog_table_name    = "gus-eip-xray-traces-data"
aws_glue_catalog_table_type       = "EXTERNAL_TABLE"
storage_descriptor_location       = "s3://gus-eip-xraytraces-data-dev/xray_parquet_data/"
storage_descriptor_input_format   = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat"
storage_descriptor_output_format  = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat"
ser_de_info_name                  = "gus-stream"
ser_de_info_serialization_library = "org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe"

#codepipeline 
r3_oaf_frontend_pipeline_name                                 = "r3-oaf-frontend"
r3_oaf_frontend_pipeline_artifact_store_location              = "r3-oaf-frontend"
r3_oaf_frontend_pipeline_artifact_store_type                  = "S3"
r3_oaf_frontend_pipeline_source_config_repository_name        = "r3-oaf-frontend"
r3_oaf_frontend_pipeline_source_config_branch_name            = "dev"
r3_oaf_frontend_pipeline_project_name                         = "r3-oaf-frontend"
r3_oaf_backend_pipeline_name                                  = "r3-oaf-backend"
r3_oaf_backend_pipeline_artifact_store_location               = "r3-oaf-backend"
r3_oaf_backend_pipeline_artifact_store_type                   = "S3"
r3_oaf_backend_pipeline_source_config_repository_name         = "r3-oaf-backend"
r3_oaf_backend_pipeline_source_config_branch_name             = "dev"
r3_oaf_backend_pipeline_project_name                          = "r3-oaf-backend"
gus_middleware_service_pipeline_name                          = "gus-middleware-service"
gus_middleware_service_pipeline_artifact_store_location       = "gus-middleware-service"
gus_middleware_service_pipeline_artifact_store_type           = "S3"
gus_middleware_service_pipeline_source_config_repository_name = "gus-middleware-service"
gus_middleware_service_pipeline_source_config_branch_name     = "dev"
gus_middleware_service_pipeline_project_name                  = "gus-middleware-service"
r3_pdf_generator_pipeline_name                                = "r3-pdf-generator"
r3_pdf_generator_pipeline_artifact_store_location             = "r3-pdf-generator"
r3_pdf_generator_pipeline_artifact_store_type                 = "S3"
r3_pdf_generator_pipeline_source_config_repository_name       = "r3-pdf-generator"
r3_pdf_generator_pipeline_source_config_branch_name           = "dev"
r3_pdf_generator_pipeline_project_name                        = "r3-pdf-generator"
gus_eip_analytics_pipeline_name                                = "gus-eip-analytics"
gus_eip_analytics_pipeline_artifact_store_location             = "gus-eip-analytics"
gus_eip_analytics_pipeline_artifact_store_type                 = "S3"
gus_eip_analytics_pipeline_source_config_repository_name       = "gus-eip-analytics"
gus_eip_analytics_pipeline_source_config_branch_name           = "dev"
gus_eip_analytics_pipeline_project_name                        = "gus-eip-analytics"
gus_eip_log_processor_pipeline_name                            = "gus-eip-log-processor"
gus_eip_log_processor_pipeline_artifact_store_location         = "gus-eip-log-processor"
gus_eip_log_processor_pipeline_artifact_store_type             = "S3"
gus_eip_log_processor_pipeline_source_config_repository_name   = "gus-eip-log-processor"
gus_eip_log_processor_pipeline_source_config_branch_name       = "dev"
gus_eip_log_processor_pipeline_project_name                    = "gus-eip-log-processor"
gus_eip_logger_frontend_pipeline_name                                = "gus-eip-logger-frontend"
gus_eip_logger_frontend_pipeline_artifact_store_location             = "gus-eip-logger-frontend"
gus_eip_logger_frontend_pipeline_artifact_store_type                 = "S3"
gus_eip_logger_frontend_pipeline_source_config_repository_name       = "gus-eip-logger-frontend"
gus_eip_logger_frontend_pipeline_source_config_branch_name           = "dev"
gus_eip_logger_frontend_pipeline_project_name                        = "gus-eip-logger-frontend"


#codebuild 
r3_oaf_frontend_project_name                                           = "r3-oaf-frontend"
r3_oaf_frontend_project_build_timeout                                  = "5"
r3_oaf_frontend_project_source_type                                    = "CODEPIPELINE"
r3_oaf_frontend_project_environment_compute_type                       = "BUILD_GENERAL1_SMALL"
r3_oaf_frontend_project_environment_image                              = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
r3_oaf_frontend_project_environment_type                               = "LINUX_CONTAINER"
r3_oaf_frontend_project_environment_image_pull_credentials_type        = "CODEBUILD"
r3_oaf_frontend_project_artifact_type                                  = "CODEPIPELINE"
r3_oaf_backend_project_name                                            = "r3-oaf-backend"
r3_oaf_backend_project_build_timeout                                   = "5"
r3_oaf_backend_project_source_type                                     = "CODEPIPELINE"
r3_oaf_backend_project_environment_compute_type                        = "BUILD_GENERAL1_SMALL"
r3_oaf_backend_project_environment_image                               = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
r3_oaf_backend_project_environment_type                                = "LINUX_CONTAINER"
r3_oaf_backend_project_environment_image_pull_credentials_type         = "CODEBUILD"
r3_oaf_backend_project_artifact_type                                   = "CODEPIPELINE"
gus_middleware_service_project_name                                    = "gus-middleware-service"
gus_middleware_service_project_build_timeout                           = "10"
gus_middleware_service_project_source_type                             = "CODEPIPELINE"
gus_middleware_service_project_environment_compute_type                = "BUILD_GENERAL1_SMALL"
gus_middleware_service_project_environment_image                       = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
gus_middleware_service_project_environment_type                        = "LINUX_CONTAINER"
gus_middleware_service_project_environment_image_pull_credentials_type = "CODEBUILD"
gus_middleware_service_project_artifact_type                           = "CODEPIPELINE"
r3_pdf_generator_project_name                                          = "r3-pdf-generator"
r3_pdf_generator_project_build_timeout                                 = "5"
r3_pdf_generator_project_source_type                                   = "CODEPIPELINE"
r3_pdf_generator_project_environment_compute_type                      = "BUILD_GENERAL1_SMALL"
r3_pdf_generator_project_environment_image                             = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
r3_pdf_generator_project_environment_type                              = "LINUX_CONTAINER"
r3_pdf_generator_project_environment_image_pull_credentials_type       = "CODEBUILD"
r3_pdf_generator_project_artifact_type                                 = "CODEPIPELINE"
gus_eip_analytics_project_name                                          = "gus-eip-analytics"
gus_eip_analytics_project_build_timeout                                 = "5"
gus_eip_analytics_project_source_type                                   = "CODEPIPELINE"
gus_eip_analytics_project_environment_compute_type                      = "BUILD_GENERAL1_SMALL"
gus_eip_analytics_project_environment_image                             = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
gus_eip_analytics_project_environment_type                              = "LINUX_CONTAINER"
gus_eip_analytics_project_environment_image_pull_credentials_type       = "CODEBUILD"
gus_eip_analytics_project_artifact_type                                 = "CODEPIPELINE"
restApiId                                                              = "ge8fq00yg4"
restApiRootResourceId                                                  = "88ha4wh6h9"
securityGroupId                                                        = "sg-095a498023011769b"
subnetId                                                               = "subnet-0ff25866b87cd982b"
gus_eip_log_processor_project_name                                          = "gus-eip-log-processor"
gus_eip_log_processor_project_build_timeout                                 = "5"
gus_eip_log_processor_project_source_type                                   = "CODEPIPELINE"
gus_eip_log_processor_project_environment_compute_type                      = "BUILD_GENERAL1_SMALL"
gus_eip_log_processor_project_environment_image                             = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
gus_eip_log_processor_project_environment_type                              = "LINUX_CONTAINER"
gus_eip_log_processor_project_environment_image_pull_credentials_type       = "CODEBUILD"
gus_eip_log_processor_project_artifact_type                                 = "CODEPIPELINE"
gus_eip_logger_frontend_project_name                                          = "gus-eip-logger-frontend"
gus_eip_logger_frontend_project_build_timeout                                 = "5"
gus_eip_logger_frontend_project_source_type                                   = "CODEPIPELINE"
gus_eip_logger_frontend_project_environment_compute_type                      = "BUILD_GENERAL1_SMALL"
gus_eip_logger_frontend_project_environment_image                             = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
gus_eip_logger_frontend_project_environment_type                              = "LINUX_CONTAINER"
gus_eip_logger_frontend_project_environment_image_pull_credentials_type       = "CODEBUILD"
gus_eip_logger_frontend_project_artifact_type                                 = "CODEPIPELINE"

#cloudfront 
s3_distribution_domain_name                                       = "r3-oaf-frontend-dev.s3-website-eu-west-1.amazonaws.com"
s3_distribution_origin_id                                         = "r3-oaf-frontend-dev.s3-website-eu-west-1.amazonaws.com"
s3_distribution_origin_protocol_policy                            = "http-only"
s3_distribution_origin_ssl_protocols                              = ["TLSv1.2"]
s3_distribution_http_port                                         = 80
s3_distribution_https_port                                        = 443
s3_distribution_enabled                                           = true
s3_distribution_is_ipv6_enabled                                   = true
gus_alternative_domain                                            = ["gusmedical-stage.apphero.io", "gusmedical-dev.apphero.io"]
s3_distribution_default_cache_behavior_allowed_methods            = ["GET", "HEAD", "OPTIONS"]
s3_distribution_default_cache_behavior_cached_methods             = ["GET", "HEAD"]
s3_distribution_default_cache_behavior_target_origin_id           = "r3-oaf-frontend-dev.s3-website-eu-west-1.amazonaws.com"
s3_distribution_viewer_protocol_policy                            = "redirect-to-https"
s3_distribution_geo_restriction_restriction_type                  = "none"
s3_distribution_viewer_certificate_cloudfront_default_certificate = true
s3_distribution_viewer_certificate_acm_certificate_arn            = "arn:aws:acm:us-east-1:************:certificate/35116c27-4b9b-4a39-8cf5-34d7cd6b2f36"
s3_distribution_default_cache_behavior_cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
s3_distribution_default_cache_behavior_response_headers_policy_id = "eaab4381-ed33-4a86-88ca-d9558dc6cd63"
s3_distribution_r3_content_security_policy                        = "frame-ancestors iapro--prodcopy--c.sandbox.vf.force.com iapro--prodcopy.sandbox.my.site.com iapro--preprod.sandbox.my.site.com iapro--preprod.sandbox.lightning.force.com;"
//iam
athena_access_accountId          = "************"
athena_access_role_name          = "AmazonGrafanaServiceRole-Y1YL2SDIf"
s3_role_arn                      = "arn:aws:iam::************:role/s3CrossAccountAccessRole-prod"
prod_lambda_assumed_role_arn     = "arn:aws:sts::************:assumed-role/gus-lambda-exec-role-prod/apphero-backend-service-prod"
dev_lambda_assumed_role_arn      = "arn:aws:sts::************:assumed-role/gus-lambda-exec-role-dev/dev-appHero-services"
dev_ecs_assumed_role_arn         = "arn:aws:iam::************:role/lim-cron-scheduler-role-dev"
prod_oap_lambda_assumed_role_arn = "arn:aws:sts::************:assumed-role/gus-lambda-exec-role-prod/oap-backend-service-prod"

#guseipapigateway
ibat_pipeline_cognito_user_pool_id = "eu-west-1_VFcjqfvty"
logs_summery_cognito_user_pool_id = "eu-west-1_kqQlIrmSO"

#cognito
cognito_ibd_custom_domain = "dev-authprovider.apphero.io"

#iam
s3_oap_cross_account_role_arn = "arn:aws:iam::************:role/s3OAPCrossAccountAccessRole-prod"

#SQS
log_processing_queue_name = "DEV-LOG-PROCESSING-QUEUE.fifo"