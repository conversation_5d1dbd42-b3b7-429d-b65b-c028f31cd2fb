
provider "aws" {
  region = var.region
}

resource "aws_s3_bucket" "gus_xray_traces" {
  bucket = "${var.gus_xray_traces_bucket_name}-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_s3_bucket" "gus_athena_query_output" {
  bucket = "${var.gus_athena_query_output_bucket_name}-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

//r3-oaf-backend
resource "aws_s3_bucket" "r3-oaf-backend" {
  bucket = "${var.r3_oaf_backend_bucket_name}-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

//gus-middleware-service
resource "aws_s3_bucket" "gus-middleware-service" {
  bucket = "${var.gus_middleware_service_bucket_name}-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

//r3-pdf-generator
resource "aws_s3_bucket" "r3-pdf-generator" {
  bucket = "${var.r3_pdf_generator_bucket_name}-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

//gus-eip-analytics
resource "aws_s3_bucket" "gus-eip-analytics" {
  bucket = "${var.gus_eip_analytics_bucket_name}-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

//gus-eip-log-processor
resource "aws_s3_bucket" "gus-eip-log-processor" {
  bucket = "${var.gus_eip_log_processor_bucket_name}-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}


//r3_oaf_frontend
resource "aws_s3_bucket" "r3_oaf_frontend" {
  bucket = "${var.r3_oaf_frontend_bucket_name}-${var.environment}" //${var.environment}
  website {
    index_document = "index.html"
    error_document = "index.html"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

output "r3_oaf_frontend_website_endpoint" {
  value = aws_s3_bucket.r3_oaf_frontend.website_endpoint
}

resource "aws_s3_bucket_ownership_controls" "r3_oaf_frontend_ownership_controls" {
  bucket = aws_s3_bucket.r3_oaf_frontend.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_public_access_block" "r3_oaf_frontend_public_access_block" {
  bucket = aws_s3_bucket.r3_oaf_frontend.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_acl" "r3_oaf_frontend_acl" {
  depends_on = [
    aws_s3_bucket_ownership_controls.r3_oaf_frontend_ownership_controls,
    aws_s3_bucket_public_access_block.r3_oaf_frontend_public_access_block,
  ]

  bucket = aws_s3_bucket.r3_oaf_frontend.id
  acl    = "public-read"
}

resource "aws_s3_bucket_policy" "r3_oaf_frontend_access_policy" {
  bucket = aws_s3_bucket.r3_oaf_frontend.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement : [
      {
        Effect : "Allow",
        Principal : "*",
        Resource : [
          aws_s3_bucket.r3_oaf_frontend.arn,
          "${aws_s3_bucket.r3_oaf_frontend.arn}/*",
        ]
        Action : [
          "s3:GetObject",
          "s3:ListBucket",
        ]
      },
    ]
  })
}

resource "aws_s3_bucket_cors_configuration" "r3_oaf_frontend_cors_policy" {
  bucket = aws_s3_bucket.r3_oaf_frontend.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["PUT", "POST", "DELETE"]
    allowed_origins = ["http://localhost:3000"]
    expose_headers  = [""]
    max_age_seconds = 3000
  }

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = ["http://localhost:3000"]
    expose_headers  = ["Access-Control-Allow-Origin"]
  }

  cors_rule {
    allowed_headers = [""]
    allowed_methods = ["GET"]
    allowed_origins = ["*"]
    expose_headers  = [""]
  }
}

resource "aws_s3_bucket" "eip_service_config_store" {
  bucket = "eip-service-config-store-${var.environment}"

  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "eip_service_config_store_encryption_configuration" {
  bucket = aws_s3_bucket.eip_service_config_store.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = "AES256"
    }
  }
}

resource "aws_s3_bucket" "gus_eip_logger_frontend" {
  bucket = "gus-eip-logger-frontend-${var.environment}"
  website {
    index_document = "index.html"
    error_document = "index.html"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

output "gus_eip_logger_frontend_website_endpoint" {
  value = aws_s3_bucket.gus_eip_logger_frontend.website_endpoint
}

resource "aws_s3_bucket_ownership_controls" "gus_eip_logger_frontend_ownership_controls" {
  bucket = aws_s3_bucket.gus_eip_logger_frontend.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_public_access_block" "gus_eip_logger_frontend_public_access_block" {
  bucket = aws_s3_bucket.gus_eip_logger_frontend.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_acl" "gus_eip_logger_frontend_acl" {
  depends_on = [
    aws_s3_bucket_ownership_controls.gus_eip_logger_frontend_ownership_controls,
    aws_s3_bucket_public_access_block.gus_eip_logger_frontend_public_access_block,
  ]

  bucket = aws_s3_bucket.gus_eip_logger_frontend.id
  acl    = "public-read"
}

resource "aws_s3_bucket_policy" "gus_eip_logger_frontend_access_policy" {
  bucket = aws_s3_bucket.gus_eip_logger_frontend.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement : [
      {
        Effect : "Allow",
        Principal : "*",
        Resource : [
          aws_s3_bucket.gus_eip_logger_frontend.arn,
          "${aws_s3_bucket.gus_eip_logger_frontend.arn}/*",
        ]
        Action : [
          "s3:GetObject",
          "s3:ListBucket"
        ]
      },
    ]
  })
}

resource "aws_s3_bucket_cors_configuration" "gus_eip_logger_frontend_cors_policy" {
  bucket = aws_s3_bucket.gus_eip_logger_frontend.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["PUT", "POST", "DELETE"]
    allowed_origins = ["http://localhost:3000"]
    expose_headers  = [""]
    max_age_seconds = 3000
  }

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = ["http://localhost:3000"]
    expose_headers  = ["Access-Control-Allow-Origin"]
  }

  cors_rule {
    allowed_headers = [""]
    allowed_methods = ["GET"]
    allowed_origins = ["*"]
    expose_headers  = [""]
  }
}
