provider "aws" {
  region = var.region
}

//r3-oaf-frontend
resource "aws_codepipeline" "r3-oaf-frontend-pipeline" {
  name     = "${var.r3_oaf_frontend_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/gus-pipeline-access-${var.environment}"
  artifact_store {
    location = "${var.r3_oaf_frontend_pipeline_artifact_store_location}-${var.environment}"
    type     = var.r3_oaf_frontend_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.r3_oaf_frontend_pipeline_source_config_repository_name
        BranchName           = var.r3_oaf_frontend_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.r3_oaf_frontend_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_rule" "r3oaf_codecommit_trigger_rule" {
  name = "r3oafCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:r3-oaf-frontend"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "r3oaf_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.r3oaf_codecommit_trigger_rule.name
  target_id = "r3oafCodecommitEventTarget"
  arn       = aws_codepipeline.r3-oaf-frontend-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/gus-codePipeline-start-access-${var.environment}"
}

//r3-oaf-backend
resource "aws_codepipeline" "r3-oaf-backend-pipeline" {
  name     = "${var.r3_oaf_backend_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/gus-pipeline-access-${var.environment}"
  artifact_store {
    location = "${var.r3_oaf_backend_pipeline_artifact_store_location}-${var.environment}"
    type     = var.r3_oaf_backend_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.r3_oaf_backend_pipeline_source_config_repository_name
        BranchName           = var.r3_oaf_backend_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.r3_oaf_backend_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_cloudwatch_event_rule" "r3_oaf_backend_codecommit_trigger_rule" {
  name = "r3oafBackendCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:r3-oaf-backend"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "r3_oaf_backend_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.r3_oaf_backend_codecommit_trigger_rule.name
  target_id = "r3oafBackendCodecommitEventTarget"
  arn       = aws_codepipeline.r3-oaf-backend-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/gus-codePipeline-start-access-${var.environment}"
}

//gus-middleware-service 
resource "aws_codepipeline" "gus-middleware-service-pipeline" {
  name     = "${var.gus_middleware_service_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/gus-pipeline-access-${var.environment}"
  artifact_store {
    location = "${var.gus_middleware_service_pipeline_artifact_store_location}-${var.environment}"
    type     = var.gus_middleware_service_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.gus_middleware_service_pipeline_source_config_repository_name
        BranchName           = var.gus_middleware_service_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.gus_middleware_service_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_rule" "gus_middleware_service_codecommit_trigger_rule" {
  name = "gusMiddleWareServiceCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:gus-middleware-service"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "gus_middleware_service_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.gus_middleware_service_codecommit_trigger_rule.name
  target_id = "gusmiddlewareserviceCodecommitEventTarget"
  arn       = aws_codepipeline.gus-middleware-service-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/gus-codePipeline-start-access-${var.environment}"
}

//r3-pdf-generator 
resource "aws_codepipeline" "r3-pdf-generator-pipeline" {
  name     = "${var.r3_pdf_generator_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/gus-pipeline-access-${var.environment}"
  artifact_store {
    location = "${var.r3_pdf_generator_pipeline_artifact_store_location}-${var.environment}"
    type     = var.r3_pdf_generator_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.r3_pdf_generator_pipeline_source_config_repository_name
        BranchName           = var.r3_pdf_generator_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.r3_pdf_generator_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_rule" "r3_pdf_generator_codecommit_trigger_rule" {
  name = "r3PdfGeneratorCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:r3-pdf-generator"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "r3_pdf_generator_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.r3_pdf_generator_codecommit_trigger_rule.name
  target_id = "r3PdfGeneratorCodecommitEventTarget"
  arn       = aws_codepipeline.r3-pdf-generator-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/gus-codePipeline-start-access-${var.environment}"
}

//gus-eip-analytics 
resource "aws_codepipeline" "gus-eip-analytics-pipeline" {
  name     = "${var.gus_eip_analytics_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/gus-pipeline-access-${var.environment}"
  artifact_store {
    location = "${var.gus_eip_analytics_pipeline_artifact_store_location}-${var.environment}"
    type     = var.gus_eip_analytics_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.gus_eip_analytics_pipeline_source_config_repository_name
        BranchName           = var.gus_eip_analytics_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.gus_eip_analytics_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_rule" "gus-eip-analytics_codecommit_trigger_rule" {
  name = "GusEipAnalyticsCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:gus-eip-analytics"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.gus_eip_analytics_pipeline_source_config_branch_name}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "gus-eip-analytics_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.gus-eip-analytics_codecommit_trigger_rule.name
  target_id = "GusEipAnalyticsCodecommitEventTarget"
  arn       = aws_codepipeline.gus-eip-analytics-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/gus-codePipeline-start-access-${var.environment}"
}

//gus-eip-log-processor 
resource "aws_codepipeline" "gus-eip-log-processor-pipeline" {
  name     = "${var.gus_eip_log_processor_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/gus-pipeline-access-${var.environment}"
  artifact_store {
    location = "${var.gus_eip_log_processor_pipeline_artifact_store_location}-${var.environment}"
    type     = var.gus_eip_log_processor_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.gus_eip_log_processor_pipeline_source_config_repository_name
        BranchName           = var.gus_eip_log_processor_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.gus_eip_log_processor_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_rule" "gus-eip-log_processor_codecommit_trigger_rule" {
  name = "GusEipLogProcessorCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:gus-eip-log-processor"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.gus_eip_log_processor_pipeline_source_config_branch_name}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "gus-eip-log_processor_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.gus-eip-log_processor_codecommit_trigger_rule.name
  target_id = "GusEipLogProcessorCodecommitEventTarget"
  arn       = aws_codepipeline.gus-eip-log-processor-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/gus-codePipeline-start-access-${var.environment}"
}

//gus_eip_logger_frontend
resource "aws_codepipeline" "gus_eip_logger_frontend_pipeline" {
  name     = "${var.gus_eip_logger_frontend_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/gus-pipeline-access-${var.environment}"
  artifact_store {
    location = "${var.gus_eip_logger_frontend_pipeline_artifact_store_location}-${var.environment}"
    type     = var.gus_eip_logger_frontend_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.gus_eip_logger_frontend_pipeline_source_config_repository_name
        BranchName           = var.gus_eip_logger_frontend_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.gus_eip_logger_frontend_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_rule" "gus_eip_logger_frontend_codecommit_trigger_rule" {
  name = "gusEipLoggerFrontendCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:gus-eip-logger-frontend"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "gus_eip_logger_frontend_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.gus_eip_logger_frontend_codecommit_trigger_rule.name
  target_id = "gusEipLoggerFrontendCodecommitEventTarget"
  arn       = aws_codepipeline.gus_eip_logger_frontend_pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/gus-codePipeline-start-access-${var.environment}"
}
