variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = ""
  type        = string
}

//r3-oaf-frontend
variable "r3_oaf_frontend_pipeline_name" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_pipeline_project_name" {
  description = ""
  type        = string
}

//r3-oaf-backend
variable "r3_oaf_backend_pipeline_name" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_pipeline_project_name" {
  description = ""
  type        = string
}

//gus-middleware-service 

variable "gus_middleware_service_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_middleware_service_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_middleware_service_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_middleware_service_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_middleware_service_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_middleware_service_pipeline_project_name" {
  description = ""
  type        = string
}

//r3-pdf-generator
variable "r3_pdf_generator_pipeline_name" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_pipeline_project_name" {
  description = ""
  type        = string
}

variable "environment_tag" {
  description = "Environment"
  type        = string
}

//gus-eip-analytics
variable "gus_eip_analytics_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_pipeline_project_name" {
  description = ""
  type        = string
}

//gus-eip-logger-frontend
variable "gus_eip_logger_frontend_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_pipeline_project_name" {
  description = ""
  type        = string
}

//gus-eip-log-processor
variable "gus_eip_log_processor_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_pipeline_project_name" {
  description = ""
  type        = string
}