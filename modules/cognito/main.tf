
//IBD Auth provider
resource "aws_cognito_user_pool" "cognito_ibd_authprovider" {
  name = "${var.environment}-ibd-authprovider"

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cognito_resource_server" "cognito_ibd_authprovider_resource_server_apikey" {
  user_pool_id  = aws_cognito_user_pool.cognito_ibd_authprovider.id
  identifier    = "x-api-key_ie8fbhpkCJ34nu8aUMH3L2lAllizvYvb19t7pioO"
  name          = "x-api-key"
  
  scope {
    scope_name        = "read"
    scope_description = "Read access to resources"
  }
}

resource "aws_cognito_resource_server" "cognito_apphero_resource_server_apikey" {
  user_pool_id  = aws_cognito_user_pool.cognito_ibd_authprovider.id
  identifier    = "x-api-key_xphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ"
  name          = "x-api-key"
  
  scope {
    scope_name        = "read"
    scope_description = "Read access to resources"
  }
}

resource "aws_cognito_resource_server" "cognito_eip_gus_sf_resource_server_apikey" {
  user_pool_id  = aws_cognito_user_pool.cognito_ibd_authprovider.id
  identifier    = "x-api-key_n2fakdBcpM54gh1qUPJ9X3vAllztyXcb20t6nkqZ"
  name          = "x-api-key"

  scope {
    scope_name        = "read"
    scope_description = "Read access to resources"
  }
}

resource "aws_cognito_resource_server" "cognito_ucw_aph_oap_resource_server_apikey" {
  user_pool_id  = aws_cognito_user_pool.cognito_ibd_authprovider.id
  identifier    = "x-api-key_rLknjx19jUaPaoqGtbLawha8ylaHwBs8zP6aoGrh"
  name          = "x-api-key"

  scope {
    scope_name        = "read"
    scope_description = "Read access to resources"
  }
}

resource "aws_cognito_user_pool_client" "cognito_ibat_pipeline_client" {
  name                              = "ibat-data-pipeline-oauth-client-${var.environment}"
  user_pool_id                      = aws_cognito_user_pool.cognito_ibd_authprovider.id
  generate_secret                   = true
  allowed_oauth_flows               = ["client_credentials"]
  allowed_oauth_scopes              = ["x-api-key_ie8fbhpkCJ34nu8aUMH3L2lAllizvYvb19t7pioO/read"]
  allowed_oauth_flows_user_pool_client = true

  depends_on = [
    aws_cognito_resource_server.cognito_ibd_authprovider_resource_server_apikey
  ]
}

resource "aws_cognito_user_pool_client" "cognito_apphero_client" {
  name                              = "apphero-oauth-client-${var.environment}"
  user_pool_id                      = aws_cognito_user_pool.cognito_ibd_authprovider.id
  generate_secret                   = true
  allowed_oauth_flows               = ["client_credentials"]
  allowed_oauth_scopes              = ["x-api-key_xphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ/read"]
  allowed_oauth_flows_user_pool_client = true

  depends_on = [
    aws_cognito_resource_server.cognito_apphero_resource_server_apikey
  ]
}

resource "aws_cognito_user_pool_client" "cognito_eip_gus_sf_client" {
  name                              = "eip-gus-sf-oauth-client-${var.environment}"
  user_pool_id                      = aws_cognito_user_pool.cognito_ibd_authprovider.id
  generate_secret                   = true
  allowed_oauth_flows               = ["client_credentials"]
  allowed_oauth_scopes              = ["x-api-key_n2fakdBcpM54gh1qUPJ9X3vAllztyXcb20t6nkqZ/read"]
  allowed_oauth_flows_user_pool_client = true

  depends_on = [
    aws_cognito_resource_server.cognito_eip_gus_sf_resource_server_apikey
  ]
}

resource "aws_cognito_user_pool_client" "cognito_ucw_aph_oap_integration_client" {
  name                              = "dev-ucw-aph-oap-integration-client"
  user_pool_id                      = aws_cognito_user_pool.cognito_ibd_authprovider.id
  generate_secret                   = true
  allowed_oauth_flows               = ["client_credentials"]
  allowed_oauth_scopes              = ["x-api-key_rLknjx19jUaPaoqGtbLawha8ylaHwBs8zP6aoGrh/read"]
  allowed_oauth_flows_user_pool_client = true

  depends_on = [
    aws_cognito_resource_server.cognito_ucw_aph_oap_resource_server_apikey
  ]
}

resource "aws_cognito_user_pool_domain" "cognito_ipd_pipeline_domain" {
  domain       = "ibd-auth-provider-${var.environment}"
  user_pool_id = aws_cognito_user_pool.cognito_ibd_authprovider.id
}

resource "aws_cognito_user_pool_domain" "custom_ibd_domain" {
  domain       = var.cognito_ibd_custom_domain
  user_pool_id = aws_cognito_user_pool.cognito_ibd_authprovider.id
  certificate_arn = var.api_gateway_certificate_acm_certificate_arn
}

