provider "aws" {
  region = var.region
}

resource "aws_api_gateway_rest_api" "api" {
  name        = "gus-eip-service-${var.environment}"
  description = "API"
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }
}

resource "aws_api_gateway_authorizer" "ibat_pipeline_app_validation_authorizer" {
  name                   = "ibat-pipeline-authorizer-${var.environment}"
  type                   = "COGNITO_USER_POOLS"
  rest_api_id            = aws_api_gateway_rest_api.api.id
  identity_source        = "method.request.header.Authorization"
  provider_arns          = [
    "arn:aws:cognito-idp:${var.region}:${var.accountId}:userpool/${var.ibat_pipeline_cognito_user_pool_id}"
  ]
}
resource "aws_api_gateway_authorizer" "logs_summery_validation_authorizer" {
  name                   = "logs-summery-authorizer-${var.environment}"
  type                   = "COGNITO_USER_POOLS"
  rest_api_id            = aws_api_gateway_rest_api.api.id
  identity_source        = "method.request.header.Authorization"
  provider_arns          = [
    "arn:aws:cognito-idp:${var.region}:${var.accountId}:userpool/${var.logs_summery_cognito_user_pool_id}"
  ]
}
resource "aws_api_gateway_authorizer" "apphero_validation_authorizer" {
  name                   = "apphero-authorizer-${var.environment}"
  type                   = "COGNITO_USER_POOLS"
  rest_api_id            = aws_api_gateway_rest_api.api.id
  identity_source        = "method.request.header.Authorization"
  provider_arns          = [
    "arn:aws:cognito-idp:${var.region}:${var.accountId}:userpool/${var.ibat_pipeline_cognito_user_pool_id}"
  ]
}
resource "aws_api_gateway_authorizer" "eip_validation_authorizer" {
  name                   = "eip-authorizer-${var.environment}"
  type                   = "COGNITO_USER_POOLS"
  rest_api_id            = aws_api_gateway_rest_api.api.id
  identity_source        = "method.request.header.Authorization"
  provider_arns          = [
    "arn:aws:cognito-idp:${var.region}:${var.accountId}:userpool/${var.ibat_pipeline_cognito_user_pool_id}"
  ]
}
resource "aws_api_gateway_method" "method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_rest_api.api.root_resource_id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_rest_api.api.root_resource_id
  http_method             = aws_api_gateway_method.method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_rest_api.api.root_resource_id
  http_method = aws_api_gateway_method.method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_rest_api.api.root_resource_id
  http_method = aws_api_gateway_method.method.http_method
  status_code = aws_api_gateway_method_response.method_response.status_code
  depends_on = [
    aws_api_gateway_integration.integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }

  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

#gus-eip-analytics
resource "aws_api_gateway_resource" "gus_eip_analytics_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "eip"
}

resource "aws_api_gateway_resource" "gus_eip_analytics_resource_logs" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.gus_eip_analytics_resource.id
  path_part   = "logs"
}

resource "aws_api_gateway_method" "gus_eip_analytics_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_eip_analytics_resource_logs.id
  http_method   = "ANY"
  authorization = "COGNITO_USER_POOLS"
  authorizer_id = aws_api_gateway_authorizer.logs_summery_validation_authorizer.id

  request_parameters = {
    "method.request.header.Authorization" = true
  }
}

resource "aws_api_gateway_integration" "gus_eip_analytics_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_eip_analytics_resource_logs.id
  http_method             = aws_api_gateway_method.gus_eip_analytics_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:eip-log-summary-fetcher-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "gus_eip_analytics_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_eip_analytics_resource_logs.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "gus_eip_analytics_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_eip_analytics_resource_logs.id
  http_method             = aws_api_gateway_method.gus_eip_analytics_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "gus_eip_analytics_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_eip_analytics_resource_logs.id
  http_method = aws_api_gateway_method.gus_eip_analytics_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "gus_eip_analytics_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_eip_analytics_resource_logs.id
  http_method = aws_api_gateway_method.gus_eip_analytics_options_method.http_method
  status_code = aws_api_gateway_method_response.gus_eip_analytics_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.gus_eip_analytics_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

#resource "aws_api_gateway_resource" "gus_eip_analytics_proxy_resource" {
#  rest_api_id = aws_api_gateway_rest_api.api.id
#  parent_id   = aws_api_gateway_resource.gus_eip_analytics_resource.id
#  path_part   = "{proxy+}"
#}

#resource "aws_api_gateway_method" "gus_eip_analytics_proxy_method" {
#  rest_api_id   = aws_api_gateway_rest_api.api.id
#  resource_id   = aws_api_gateway_resource.gus_eip_analytics_proxy_resource.id
#  http_method   = "ANY"
#  authorization = "NONE"
#}

#resource "aws_api_gateway_integration" "gus_eip_analytics_proxy_integration" {
#  rest_api_id             = aws_api_gateway_rest_api.api.id
#  resource_id             = aws_api_gateway_resource.gus_eip_analytics_proxy_resource.id
#  http_method             = aws_api_gateway_method.gus_eip_analytics_proxy_method.http_method
#  type                    = "AWS_PROXY"
#  integration_http_method = "POST"
#  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:eip-log-summary-fetcher-${var.environment}/invocations"
#}

/*resource "aws_api_gateway_method" "gus_eip_analytics_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_eip_analytics_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "gus_eip_analytics_proxy_options_method_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_eip_analytics_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_eip_analytics_proxy_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "gus_eip_analytics_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_eip_analytics_proxy_resource.id
  http_method = aws_api_gateway_method.gus_eip_analytics_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "gus_eip_analytics_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_eip_analytics_proxy_resource.id
  http_method = aws_api_gateway_method.gus_eip_analytics_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.gus_eip_analytics_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.gus_eip_analytics_proxy_options_method_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}*/

#eip student details api
resource "aws_api_gateway_resource" "eip_studentdetail_root_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "profile"
}

resource "aws_api_gateway_resource" "eip_studentdetail_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.eip_studentdetail_root_resource.id
  path_part   = "getstudentdetail"
}

resource "aws_api_gateway_resource" "eip_studentdetail_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.eip_studentdetail_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "eip_studentdetail_any_method" {
  rest_api_id      = aws_api_gateway_rest_api.api.id
  resource_id      = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method      = "ANY"
  authorization    = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "eip_studentdetail_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method             = aws_api_gateway_method.eip_studentdetail_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "eip_studentdetail_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "eip_studentdetail_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method             = aws_api_gateway_method.eip_studentdetail_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "eip_studentdetail_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method = aws_api_gateway_method.eip_studentdetail_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "eip_studentdetail_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method = aws_api_gateway_method.eip_studentdetail_options_method.http_method
  status_code = aws_api_gateway_method_response.eip_studentdetail_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.eip_studentdetail_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
# cobranding
resource "aws_api_gateway_resource" "apphero_root_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "apphero"
}
resource "aws_api_gateway_resource" "apphero_cobrand_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.apphero_root_resource.id
  path_part   = "cobrandedurl"
}
resource "aws_api_gateway_method" "apphero_cobrand_any_method" {
  rest_api_id      = aws_api_gateway_rest_api.api.id
  resource_id      = aws_api_gateway_resource.apphero_cobrand_resource.id
  http_method      = "ANY"
  authorization    = "COGNITO_USER_POOLS"
  api_key_required = false
  authorizer_id = aws_api_gateway_authorizer.apphero_validation_authorizer.id
  authorization_scopes = ["x-api-key_xphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ/read"]
}
resource "aws_api_gateway_integration" "apphero_cobrand_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.apphero_cobrand_resource.id
  http_method             = aws_api_gateway_method.apphero_cobrand_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "apphero_cobrand_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.apphero_cobrand_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "apphero_cobrand_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.apphero_cobrand_resource.id
  http_method             = aws_api_gateway_method.apphero_cobrand_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "apphero_cobrand_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.apphero_cobrand_resource.id
  http_method = aws_api_gateway_method.apphero_cobrand_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "apphero_cobrand_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.apphero_cobrand_resource.id
  http_method = aws_api_gateway_method.apphero_cobrand_options_method.http_method
  status_code = aws_api_gateway_method_response.apphero_cobrand_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.apphero_cobrand_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#IBAT pipeline
resource "aws_api_gateway_resource" "ibat_pipeline_root_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "ibat"
}

resource "aws_api_gateway_resource" "ibat_pipeline_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.ibat_pipeline_root_resource.id
  path_part   = "studentapplications"
}

resource "aws_api_gateway_method" "ibat_pipeline_any_method" {
  rest_api_id      = aws_api_gateway_rest_api.api.id
  resource_id      = aws_api_gateway_resource.ibat_pipeline_resource.id
  http_method      = "ANY"
  authorization    = "COGNITO_USER_POOLS"
  api_key_required = false
  authorizer_id = aws_api_gateway_authorizer.ibat_pipeline_app_validation_authorizer.id
  authorization_scopes = ["x-api-key_ie8fbhpkCJ34nu8aUMH3L2lAllizvYvb19t7pioO/read"]
}

resource "aws_api_gateway_integration" "ibat_pipeline_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.ibat_pipeline_resource.id
  http_method             = aws_api_gateway_method.ibat_pipeline_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "ibat_pipeline_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.ibat_pipeline_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "ibat_pipeline_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.ibat_pipeline_resource.id
  http_method             = aws_api_gateway_method.ibat_pipeline_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "ibat_pipeline_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.ibat_pipeline_resource.id
  http_method = aws_api_gateway_method.ibat_pipeline_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "ibat_pipeline_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.ibat_pipeline_resource.id
  http_method = aws_api_gateway_method.ibat_pipeline_options_method.http_method
  status_code = aws_api_gateway_method_response.ibat_pipeline_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.ibat_pipeline_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_resource" "ibat_pipeline_app_validation_api_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.ibat_pipeline_root_resource.id
  path_part   = "gusidvalidation"
}

resource "aws_api_gateway_method" "ibat_pipeline_app_validation_api_any_method" {
  rest_api_id      = aws_api_gateway_rest_api.api.id
  resource_id      = aws_api_gateway_resource.ibat_pipeline_app_validation_api_resource.id
  http_method      = "ANY"
  authorization    = "COGNITO_USER_POOLS"
  authorizer_id = aws_api_gateway_authorizer.ibat_pipeline_app_validation_authorizer.id
  authorization_scopes = ["x-api-key_ie8fbhpkCJ34nu8aUMH3L2lAllizvYvb19t7pioO/read"]
}

resource "aws_api_gateway_integration" "ibat_pipeline_app_validation_api_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.ibat_pipeline_app_validation_api_resource.id
  http_method             = aws_api_gateway_method.ibat_pipeline_app_validation_api_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "ibat_pipeline_app_validation_api_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.ibat_pipeline_app_validation_api_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "ibat_pipeline_app_validation_api_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.ibat_pipeline_app_validation_api_resource.id
  http_method             = aws_api_gateway_method.ibat_pipeline_app_validation_api_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
        "application/json" = jsonencode({
      statusCode = 200
        })
    }
}

resource "aws_api_gateway_method_response" "ibat_pipeline_app_validation_api_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.ibat_pipeline_app_validation_api_resource.id
  http_method = aws_api_gateway_method.ibat_pipeline_app_validation_api_options_method.http_method
  status_code = "200"
  response_models = {
        "application/json" = "Empty"
    }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
    }
}

resource "aws_api_gateway_integration_response" "ibat_pipeline_app_validation_api_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.ibat_pipeline_app_validation_api_resource.id
  http_method = aws_api_gateway_method.ibat_pipeline_app_validation_api_options_method.http_method
  status_code = aws_api_gateway_method_response.ibat_pipeline_app_validation_api_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.ibat_pipeline_app_validation_api_options_integration
    ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods": "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers": "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin": "'*'"
    }
  response_templates = {
        "application/json" = jsonencode({
      statusCode = 200
        })
    }
}
#GUS SALESFORCE 
resource "aws_api_gateway_resource" "gus_salesforce_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "salesforce"
}

resource "aws_api_gateway_resource" "gus_salesforce_api_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.gus_salesforce_resource.id
  path_part   = "api"
}

resource "aws_api_gateway_method" "gus_salesforce_api_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_salesforce_api_resource.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "gus_salesforce_api_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_salesforce_api_resource.id
  http_method             = aws_api_gateway_method.gus_salesforce_api_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

#GUS Salesforce API GET Proxy
resource "aws_api_gateway_resource" "gus_salesforce_api_get_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.gus_salesforce_api_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "gus_salesforce_api_get_proxy_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
  request_parameters = {
    "method.request.path.proxy" = true
  }
}

resource "aws_api_gateway_integration" "gus_salesforce_api_get_proxy_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_salesforce_api_get_proxy_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "gus_salesforce_api_get_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "gus_salesforce_api_get_proxy_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_salesforce_api_get_proxy_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "gus_salesforce_api_get_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method = aws_api_gateway_method.gus_salesforce_api_get_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "gus_salesforce_api_get_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method = aws_api_gateway_method.gus_salesforce_api_get_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.gus_salesforce_api_get_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.gus_salesforce_api_get_proxy_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

#GUS Salesforce Proxy
resource "aws_api_gateway_resource" "gus_salesforce_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.gus_salesforce_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "gus_salesforce_proxy_any_method" {
  rest_api_id      = aws_api_gateway_rest_api.api.id
  resource_id      = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method      = "ANY"
  authorization    = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "gus_salesforce_proxy_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_salesforce_proxy_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "gus_salesforce_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "gus_salesforce_proxy_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_salesforce_proxy_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "gus_salesforce_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method = aws_api_gateway_method.gus_salesforce_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "gus_salesforce_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method = aws_api_gateway_method.gus_salesforce_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.gus_salesforce_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.gus_salesforce_proxy_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

#Trulioo KYB KYC APIs
resource "aws_api_gateway_resource" "trulioo_kyb_kyc_root_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "gus"
}

resource "aws_api_gateway_resource" "trulioo_kyb_kyc_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.trulioo_kyb_kyc_root_resource.id
  path_part   = "trulioo"
}

#Trulioo webhook api
resource "aws_api_gateway_resource" "trulioo_kyb_notifyverification_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.trulioo_kyb_kyc_resource.id
  path_part   = "agencykyb"
}

resource "aws_api_gateway_resource" "trulioo_kyb_notifyverification_child_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.trulioo_kyb_notifyverification_resource.id
  path_part   = "notifyverification"
}

resource "aws_api_gateway_resource" "trulioo_kyb_notifyverification_enquirytype_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.trulioo_kyb_notifyverification_child_resource.id
  path_part   = "{enquiryTypeId}"
}

resource "aws_api_gateway_method" "trulioo_kyb_notifyverification_post_method" {
  rest_api_id      = aws_api_gateway_rest_api.api.id
  resource_id      = aws_api_gateway_resource.trulioo_kyb_notifyverification_enquirytype_resource.id
  http_method      = "POST"
  authorization    = "NONE"
}

resource "aws_api_gateway_integration" "trulioo_kyb_notifyverification_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.trulioo_kyb_notifyverification_enquirytype_resource.id
  http_method             = aws_api_gateway_method.trulioo_kyb_notifyverification_post_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "trulioo_kyb_notifyverification_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.trulioo_kyb_notifyverification_enquirytype_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "trulioo_kyb_notifyverification_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.trulioo_kyb_notifyverification_enquirytype_resource.id
  http_method             = aws_api_gateway_method.trulioo_kyb_notifyverification_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "trulioo_kyb_notifyverification_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.trulioo_kyb_notifyverification_enquirytype_resource.id
  http_method = aws_api_gateway_method.trulioo_kyb_notifyverification_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "trulioo_kyb_notifyverification_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.trulioo_kyb_notifyverification_enquirytype_resource.id
  http_method = aws_api_gateway_method.trulioo_kyb_notifyverification_options_method.http_method
  status_code = aws_api_gateway_method_response.trulioo_kyb_notifyverification_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.trulioo_kyb_notifyverification_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

#Trulioo enrollment webhook api

resource "aws_api_gateway_resource" "trulioo_kyb_enrollment_notifyverification_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.trulioo_kyb_kyc_resource.id
  path_part   = "agencykybenrollment"
}

resource "aws_api_gateway_resource" "trulioo_kyb_enrollment_notifyverification_child_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.trulioo_kyb_enrollment_notifyverification_resource.id
  path_part   = "notifyverification"
}

resource "aws_api_gateway_resource" "trulioo_kyb_enrollment_notifyverification_enquirytype_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.trulioo_kyb_enrollment_notifyverification_child_resource.id
  path_part   = "{enquiryTypeId}"
}

resource "aws_api_gateway_method" "trulioo_kyb_enrollment_notifyverification_post_method" {
  rest_api_id      = aws_api_gateway_rest_api.api.id
  resource_id      = aws_api_gateway_resource.trulioo_kyb_enrollment_notifyverification_enquirytype_resource.id
  http_method      = "POST"
  authorization    = "NONE"
}

resource "aws_api_gateway_integration" "trulioo_kyb_enrollment_notifyverification_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.trulioo_kyb_enrollment_notifyverification_enquirytype_resource.id
  http_method             = aws_api_gateway_method.trulioo_kyb_enrollment_notifyverification_post_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "trulioo_kyb_enrollment_notifyverification_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.trulioo_kyb_enrollment_notifyverification_enquirytype_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "trulioo_kyb_enrollment_notifyverification_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.trulioo_kyb_enrollment_notifyverification_enquirytype_resource.id
  http_method             = aws_api_gateway_method.trulioo_kyb_enrollment_notifyverification_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "trulioo_kyb_enrollment_notifyverification_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.trulioo_kyb_enrollment_notifyverification_enquirytype_resource.id
  http_method = aws_api_gateway_method.trulioo_kyb_enrollment_notifyverification_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "trulioo_kyb_enrollment_notifyverification_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.trulioo_kyb_enrollment_notifyverification_enquirytype_resource.id
  http_method = aws_api_gateway_method.trulioo_kyb_enrollment_notifyverification_options_method.http_method
  status_code = aws_api_gateway_method_response.trulioo_kyb_enrollment_notifyverification_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.trulioo_kyb_enrollment_notifyverification_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}


#Trulioo proxy resource

resource "aws_api_gateway_resource" "trulioo_kyb_kyc_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.trulioo_kyb_kyc_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "trulioo_kyb_kyc_any_method" {
  rest_api_id      = aws_api_gateway_rest_api.api.id
  resource_id      = aws_api_gateway_resource.trulioo_kyb_kyc_proxy_resource.id
  http_method      = "ANY"
  authorization    = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "trulioo_kyb_kyc_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.trulioo_kyb_kyc_proxy_resource.id
  http_method             = aws_api_gateway_method.trulioo_kyb_kyc_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "trulioo_kyb_kyc_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.trulioo_kyb_kyc_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "trulioo_kyb_kyc_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.trulioo_kyb_kyc_proxy_resource.id
  http_method             = aws_api_gateway_method.trulioo_kyb_kyc_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "trulioo_kyb_kyc_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.trulioo_kyb_kyc_proxy_resource.id
  http_method = aws_api_gateway_method.trulioo_kyb_kyc_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "trulioo_kyb_kyc_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.trulioo_kyb_kyc_proxy_resource.id
  http_method = aws_api_gateway_method.trulioo_kyb_kyc_options_method.http_method
  status_code = aws_api_gateway_method_response.trulioo_kyb_kyc_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.trulioo_kyb_kyc_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

#EnglishtestValidation

resource "aws_api_gateway_resource" "cert_validation_student_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.trulioo_kyb_kyc_root_resource.id
  path_part   = "student"
}


resource "aws_api_gateway_resource" "cert_validation_entestcert_child_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.cert_validation_student_resource.id
  path_part   = "certificate"
}

resource "aws_api_gateway_resource" "cert_validation_dovalidate_child_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.cert_validation_entestcert_child_resource.id
  path_part   = "validation"
}

resource "aws_api_gateway_method" "cert_validation_post_method" {
  rest_api_id      = aws_api_gateway_rest_api.api.id
  resource_id      = aws_api_gateway_resource.cert_validation_dovalidate_child_resource.id
  http_method      = "POST"
  authorization    = "COGNITO_USER_POOLS"
  authorizer_id = aws_api_gateway_authorizer.eip_validation_authorizer.id
  authorization_scopes = ["x-api-key_n2fakdBcpM54gh1qUPJ9X3vAllztyXcb20t6nkqZ/read"]
}

resource "aws_api_gateway_integration" "cert_validation_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.cert_validation_dovalidate_child_resource.id
  http_method             = aws_api_gateway_method.cert_validation_post_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "cert_validation_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.cert_validation_dovalidate_child_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "cert_validation_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.cert_validation_dovalidate_child_resource.id
  http_method             = aws_api_gateway_method.cert_validation_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "cert_validation_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.cert_validation_dovalidate_child_resource.id
  http_method = aws_api_gateway_method.cert_validation_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cert_validation_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.cert_validation_dovalidate_child_resource.id
  http_method = aws_api_gateway_method.cert_validation_options_method.http_method
  status_code = aws_api_gateway_method_response.cert_validation_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.cert_validation_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

#ocr webhook
resource "aws_api_gateway_resource" "ocr_process_root_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.trulioo_kyb_kyc_root_resource.id
  path_part   = "ocr"
}

resource "aws_api_gateway_resource" "ocr_process_result_child_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.ocr_process_root_resource.id
  path_part   = "process"
}

resource "aws_api_gateway_resource" "ocr_process_result_validation_child_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.ocr_process_result_child_resource.id
  path_part   = "result"
}

resource "aws_api_gateway_method" "ocr_process_result_post_method" {
  rest_api_id      = aws_api_gateway_rest_api.api.id
  resource_id      = aws_api_gateway_resource.ocr_process_result_validation_child_resource.id
  http_method      = "POST"
  authorization    = "COGNITO_USER_POOLS"
  authorizer_id    = aws_api_gateway_authorizer.eip_validation_authorizer.id
  authorization_scopes = ["x-api-key_n2fakdBcpM54gh1qUPJ9X3vAllztyXcb20t6nkqZ/read"]
}

resource "aws_api_gateway_integration" "ocr_process_result_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.ocr_process_result_validation_child_resource.id
  http_method             = aws_api_gateway_method.ocr_process_result_post_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:ocr-integration-service-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "ocr_process_result_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.ocr_process_result_validation_child_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "ocr_process_result_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.ocr_process_result_validation_child_resource.id
  http_method             = aws_api_gateway_method.ocr_process_result_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "ocr_process_result_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.ocr_process_result_validation_child_resource.id
  http_method = aws_api_gateway_method.ocr_process_result_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "ocr_process_result_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.ocr_process_result_validation_child_resource.id
  http_method = aws_api_gateway_method.ocr_process_result_options_method.http_method
  status_code = aws_api_gateway_method_response.ocr_process_result_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.ocr_process_result_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}


#ocr APIs

resource "aws_api_gateway_resource" "ocr_process_proxy" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.ocr_process_root_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "ocr_process_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.ocr_process_proxy.id
  http_method   = "ANY"
  authorization    = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "ocr_process_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.ocr_process_proxy.id
  http_method             = aws_api_gateway_method.ocr_process_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:ocr-integration-service-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "ocr_process_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.ocr_process_proxy.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "ocr_process_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.ocr_process_proxy.id
  http_method             = aws_api_gateway_method.ocr_process_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "ocr_process_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.ocr_process_proxy.id
  http_method = aws_api_gateway_method.ocr_process_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "ocr_process_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.ocr_process_proxy.id
  http_method = aws_api_gateway_method.ocr_process_options_method.http_method
  status_code = aws_api_gateway_method_response.ocr_process_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.ocr_process_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

# /gus/ocr/results
resource "aws_api_gateway_resource" "gus_ocr_results" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.ocr_process_root_resource.id
  path_part   = "results"
}


resource "aws_api_gateway_method" "gus_ocr_results_any_method" {
  rest_api_id      = aws_api_gateway_rest_api.api.id
  resource_id      = aws_api_gateway_resource.gus_ocr_results.id
  http_method      = "ANY"
  authorization    = "NONE"
  api_key_required = true
}


resource "aws_api_gateway_integration" "gus_ocr_results_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_ocr_results.id
  http_method             = aws_api_gateway_method.gus_ocr_results_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}


resource "aws_api_gateway_method" "gus_ocr_results_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_ocr_results.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "gus_ocr_results_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_ocr_results.id
  http_method             = aws_api_gateway_method.gus_ocr_results_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({ statusCode = 200 })
  }
}

resource "aws_api_gateway_method_response" "gus_ocr_results_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_ocr_results.id
  http_method = aws_api_gateway_method.gus_ocr_results_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "gus_ocr_results_options_integration_response" {
  depends_on = [
    aws_api_gateway_integration.gus_ocr_results_options_integration
  ]
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_ocr_results.id
  http_method = aws_api_gateway_method.gus_ocr_results_options_method.http_method
  status_code = aws_api_gateway_method_response.gus_ocr_results_options_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin"  : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({ statusCode = 200 })
  }
}


#EnglishTest auth APIs
resource "aws_api_gateway_resource" "proficiency_qualification_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.trulioo_kyb_kyc_root_resource.id
  path_part   = "proficiencyQualification"
}

resource "aws_api_gateway_resource" "proficiency_qualification_proxy" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.proficiency_qualification_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "proficiency_qualification_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.proficiency_qualification_proxy.id
  http_method   = "ANY"
  authorization    = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "proficiency_qualification_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.proficiency_qualification_proxy.id
  http_method             = aws_api_gateway_method.proficiency_qualification_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "proficiency_qualification_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.proficiency_qualification_proxy.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "proficiency_qualification_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.proficiency_qualification_proxy.id
  http_method             = aws_api_gateway_method.proficiency_qualification_options_method.http_method
  type                    = "MOCK"
  integration_http_method = "OPTIONS"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "proficiency_qualification_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.proficiency_qualification_proxy.id
  http_method = aws_api_gateway_method.proficiency_qualification_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "proficiency_qualification_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.proficiency_qualification_proxy.id
  http_method = aws_api_gateway_method.proficiency_qualification_options_method.http_method
  status_code = aws_api_gateway_method_response.proficiency_qualification_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.proficiency_qualification_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

#gateway response
resource "aws_api_gateway_gateway_response" "access_denied_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "ACCESS_DENIED"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "unauthorized_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "UNAUTHORIZED"
  status_code   = 401
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "waf_filtered_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "WAF_FILTERED"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "request_too_large_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "REQUEST_TOO_LARGE"
  status_code   = 413
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "invalid_apikey_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "INVALID_API_KEY"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "default_4xx_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "DEFAULT_4XX"
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "quota_exceeded_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "QUOTA_EXCEEDED"
  status_code   = 429
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "throttled_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "THROTTLED"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}



resource "aws_api_gateway_gateway_response" "bad_request_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "BAD_REQUEST_BODY"
  status_code   = 400
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "invalid_signature_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "INVALID_SIGNATURE"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "missing_athentication_token_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "MISSING_AUTHENTICATION_TOKEN"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "bad_request_parameter_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "BAD_REQUEST_PARAMETERS"
  status_code   = 400
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "default_5xx_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "DEFAULT_5XX"
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "expired_token_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "EXPIRED_TOKEN"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "resource_not_found_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "RESOURCE_NOT_FOUND"
  status_code   = 404
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token','Correlation-Id'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}
resource "null_resource" "delete_stage" {
  provisioner "local-exec" {
    command = "echo 'Hello, Terraform!'"
  }
}

resource "aws_api_gateway_method_settings" "eip_gateway_settings" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  stage_name  = var.environment
  method_path = "*/*"
  settings {
    logging_level = "INFO"
    data_trace_enabled = true
    metrics_enabled = true
  }
}

resource "aws_api_gateway_deployment" "eip_deployment" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  variables = {
    deployed_at = timestamp()
  }
  triggers = {
    redeployment = sha1(jsonencode(aws_api_gateway_rest_api.api.body))
  }
  lifecycle {
    create_before_destroy = true
  }
  depends_on = [
    aws_api_gateway_method.method,
    aws_api_gateway_method.eip_studentdetail_any_method,
    aws_api_gateway_method.eip_studentdetail_options_method,
    aws_api_gateway_method.gus_salesforce_api_method,
    aws_api_gateway_method.gus_salesforce_api_get_proxy_any_method,
    aws_api_gateway_method.gus_salesforce_proxy_any_method,
    aws_api_gateway_method.gus_salesforce_proxy_options_method,
    aws_api_gateway_method.ibat_pipeline_any_method,
    aws_api_gateway_method.ibat_pipeline_options_method,
    aws_api_gateway_method.ibat_pipeline_app_validation_api_any_method,
    aws_api_gateway_method.ibat_pipeline_app_validation_api_options_method,
    aws_api_gateway_method.apphero_cobrand_any_method,
    aws_api_gateway_method.apphero_cobrand_options_method,
    aws_api_gateway_method.trulioo_kyb_notifyverification_post_method,
    aws_api_gateway_method.trulioo_kyb_notifyverification_options_method,
    aws_api_gateway_method.trulioo_kyb_enrollment_notifyverification_post_method,
    aws_api_gateway_method.trulioo_kyb_enrollment_notifyverification_options_method,
    aws_api_gateway_method.trulioo_kyb_kyc_any_method,
    aws_api_gateway_method.trulioo_kyb_kyc_options_method,
    aws_api_gateway_method.cert_validation_post_method,
    aws_api_gateway_method.cert_validation_options_method,
    aws_api_gateway_method.ocr_process_result_post_method,
    aws_api_gateway_method.ocr_process_result_options_method,
    aws_api_gateway_method.ocr_process_any_method,
    aws_api_gateway_method.ocr_process_options_method,
    aws_api_gateway_method.gus_ocr_results_any_method,
    aws_api_gateway_method.gus_ocr_results_options_method,
    aws_api_gateway_method.proficiency_qualification_any_method,
    aws_api_gateway_method.proficiency_qualification_options_method,
    aws_api_gateway_integration.integration,
    aws_api_gateway_integration.eip_studentdetail_integration,
    aws_api_gateway_integration.eip_studentdetail_options_integration,
    aws_api_gateway_integration.gus_salesforce_api_integration,
    aws_api_gateway_integration.gus_salesforce_api_get_proxy_any_integration,
    aws_api_gateway_integration.gus_salesforce_api_get_proxy_options_integration,
    aws_api_gateway_integration.gus_salesforce_proxy_any_integration,
    aws_api_gateway_integration.gus_salesforce_proxy_options_integration,
    aws_api_gateway_integration.ibat_pipeline_integration,
    aws_api_gateway_integration.ibat_pipeline_options_integration,
    aws_api_gateway_integration.ibat_pipeline_app_validation_api_integration,
    aws_api_gateway_integration.ibat_pipeline_app_validation_api_options_integration,
    aws_api_gateway_integration.apphero_cobrand_integration,
    aws_api_gateway_integration.apphero_cobrand_options_integration,
    aws_api_gateway_integration.trulioo_kyb_notifyverification_integration,
    aws_api_gateway_integration.trulioo_kyb_notifyverification_options_integration,
    aws_api_gateway_integration.trulioo_kyb_enrollment_notifyverification_integration,
    aws_api_gateway_integration.trulioo_kyb_enrollment_notifyverification_options_integration,
    aws_api_gateway_integration.trulioo_kyb_kyc_integration,
    aws_api_gateway_integration.trulioo_kyb_kyc_options_integration,
    aws_api_gateway_integration.cert_validation_integration,
    aws_api_gateway_integration.cert_validation_options_integration,
    aws_api_gateway_integration.ocr_process_result_integration,
    aws_api_gateway_integration.ocr_process_result_options_integration,
    aws_api_gateway_integration.proficiency_qualification_any_integration,
    aws_api_gateway_integration.proficiency_qualification_options_integration,
    aws_api_gateway_integration.ocr_process_any_integration,
    aws_api_gateway_integration.ocr_process_options_integration,
    aws_api_gateway_integration.gus_ocr_results_any_integration,
    aws_api_gateway_integration.gus_ocr_results_options_integration,
    aws_api_gateway_method.gus_eip_analytics_method,
    aws_api_gateway_integration.gus_eip_analytics_integration,
    aws_api_gateway_method.gus_eip_analytics_options_method,
    aws_api_gateway_integration.gus_eip_analytics_options_integration,
    #aws_api_gateway_method.gus_eip_analytics_proxy_method,
    #aws_api_gateway_integration.gus_eip_analytics_proxy_integration,
    #aws_api_gateway_method.gus_eip_analytics_proxy_options_method,
    #aws_api_gateway_integration.gus_eip_analytics_proxy_options_method_integration,
  ]
}
resource "aws_api_gateway_stage" "eip_deployment_stage" {
  deployment_id        = aws_api_gateway_deployment.eip_deployment.id
  stage_name           = var.environment
  rest_api_id          = aws_api_gateway_rest_api.api.id
  xray_tracing_enabled = true
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_domain_name" "eip_domain" {
  domain_name     = var.eip_gateway_custom_domain
  certificate_arn = var.eip_gateway_certificate_acm_certificate_arn
  endpoint_configuration {
    types = ["EDGE"]
  }
}
resource "aws_api_gateway_base_path_mapping" "eip_gateway_mapping" {
  api_id      =   aws_api_gateway_rest_api.api.id
  domain_name = aws_api_gateway_domain_name.eip_domain.id
  stage_name  = aws_api_gateway_stage.eip_deployment_stage.stage_name
}
resource "aws_api_gateway_usage_plan" "development" {
  name         = "r3oaf"
  description  = "r3 usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "R3OAF"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_usage_plan" "appHeroUsagePlan" {
  name         = "appHero"
  description  = "apphero usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "APPHERO"
    Team        = "EIP Development Team"
  }
}

resource "aws_api_gateway_usage_plan" "gusUniversalUsagePlan" {
  name         = "gusUniversal"
  description  = "gusUniversal usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "APPHERO"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_usage_plan" "EIPIntegrationUsagePlan" {
  name         = "EIPIntegration"
  description  = "EIP Integraiton Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }

}
resource "aws_api_gateway_usage_plan" "OAPIntegrationUsagePlan" {
  name         = "OAPIntegration"
  description  = "External OAP Integraiton Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }

}
resource "aws_api_gateway_usage_plan" "IbatElUsagePlan" {
  name         = "IbatEl"
  description  = "IBAT EL Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }

}
resource "aws_api_gateway_usage_plan" "LimUsagePlan" {
  name         = "Lim"
  description  = "LIM Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }

}
resource "aws_api_gateway_usage_plan" "HzuUsagePlan" {
  name         = "Hzu"
  description  = "Hzu Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_usage_plan" "ArdUsagePlan" {
  name         = "Ard"
  description  = "Ard Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_usage_plan" "WulUsagePlan" {
  name         = "Wul"
  description  = "Wul Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_usage_plan" "IbatPipelineUsagePlan" {
  name         = "IbatPipeline"
  description  = "LIM Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }

}
resource "aws_api_gateway_usage_plan" "UCWPipelineUsagePlan" {
  name         = "UCWPipeline"
  description  = "UCW Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }

}
resource "aws_api_gateway_usage_plan" "UnfcUsagePlan" {
  name         = "Unfc"
  description  = "Unfc Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }

}

resource "aws_api_gateway_usage_plan" "UeUsagePlan" {
  name         = "Ue"
  description  = "Ue Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }

}

resource "aws_api_gateway_usage_plan" "LsbfmyrUsagePlan" {
  name         = "Lsbfmyr"
  description  = "Unfc Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }

}

resource "aws_api_gateway_api_key" "apphero_api_key" {
  name  = "APPHERO-API-KEY"
  value = var.apphero_consumer_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "APPHERO"
    Team        = "EIP Development Team"
  }
}

resource "aws_api_gateway_api_key" "gus_eip_sf_api_key" {
  name  = "GUS_EIP_SF_KEY"
  value = var.gus_eip_sf_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }
}

resource "aws_api_gateway_api_key" "oap_integration_api_key" {
  name  = "OAP-INTEGRATION-API-KEY"
  value = var.oap_integration_consumer_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }
}

resource "aws_api_gateway_api_key" "gus_universal_api_key" {
  name  = "GUS-UNIVERSAL-API-KEY"
  value = var.gus_universal_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }
}

resource "aws_api_gateway_api_key" "development_gateway_api_key" {
  name  = "R3OAF-API-KEY"
  value = var.r3_consumer_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "R3OAF"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_api_key" "ibat_el_api_key" {
  name  = "IBAT-EL-API-KEY"
  value = var.ibat_el_consumer_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_api_key" "lim_api_key" {
  name  = "LIM-API-KEY"
  value = var.lim_consumer_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_api_key" "hzu_api_key" {
  name  = "HZU-API-KEY"
  value = var.hzu_consumer_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_api_key" "ard_api_key" {
  name  = "ARD-API-KEY"
  value = var.ard_consumer_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_api_key" "wul_api_key" {
  name  = "WUL-API-KEY"
  value = var.wul_consumer_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_api_key" "ue_api_key" {
  name  = "UE-API-KEY"
  value = var.ue_consumer_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_api_key" "unfc_api_key" {
  name  = "UNFC-API-KEY"
  value = var.unfc_consumer_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_api_key" "ibat_pipeline_api_key" {
  name  = "IBAT-PIPELINE-API-KEY"
  value = var.ibat_pipeline_consumer_api_key
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_api_key" "ucw_pipeline_api_key" {
  name  = "UCW-PIPELINE-API-KEY"
  value = "rLknjx19jUaPaoqGtbLawha8ylaHwBs8zP6aoGrh"
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_api_key" "lsbfmyr_pipeline_api_key" {
  name  = "LSBFMYR-PIPELINE-API-KEY"
  value = "rLknjx19jUaPaoqGtbLawha8ylaHwBs8zP7aoGrh"
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_usage_plan_key" "apphero" {
  key_id        = aws_api_gateway_api_key.apphero_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.appHeroUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "main" {
  key_id        = aws_api_gateway_api_key.development_gateway_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.development.id
}
resource "aws_api_gateway_usage_plan_key" "oap" {
  key_id        = aws_api_gateway_api_key.oap_integration_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.OAPIntegrationUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "eip" {
  key_id        = aws_api_gateway_api_key.gus_eip_sf_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.EIPIntegrationUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "gusUniversal" {
  key_id        = aws_api_gateway_api_key.gus_universal_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.gusUniversalUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "ibatEl" {
  key_id        = aws_api_gateway_api_key.ibat_el_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.IbatElUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "lim" {
  key_id        = aws_api_gateway_api_key.lim_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.LimUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "hzu" {
  key_id        = aws_api_gateway_api_key.hzu_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.HzuUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "ard" {
  key_id        = aws_api_gateway_api_key.ard_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.ArdUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "wul" {
  key_id        = aws_api_gateway_api_key.wul_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.WulUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "unfc" {
  key_id        = aws_api_gateway_api_key.unfc_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.UnfcUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "ue" {
  key_id        = aws_api_gateway_api_key.ue_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.UeUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "ibat-pipeline" {
  key_id        = aws_api_gateway_api_key.ibat_pipeline_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.IbatPipelineUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "ucw-pipeline" {
  key_id        = aws_api_gateway_api_key.ucw_pipeline_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.UCWPipelineUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "lsbfmyr-pipeline" {
  key_id        = aws_api_gateway_api_key.lsbfmyr_pipeline_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.LsbfmyrUsagePlan.id
}
output "invoke_url" {
  value = aws_api_gateway_deployment.eip_deployment.invoke_url
}

# UNFC SFTP Route
resource "aws_api_gateway_resource" "unfc_sftp_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.gus_eip_analytics_resource.id
  path_part   = "unfc"
}

resource "aws_api_gateway_resource" "unfc_sftp_child_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.unfc_sftp_resource.id
  path_part   = "sftp"
}

resource "aws_api_gateway_method" "unfc_sftp_post_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.unfc_sftp_child_resource.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "unfc_sftp_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.unfc_sftp_child_resource.id
  http_method             = aws_api_gateway_method.unfc_sftp_post_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:unfc-sftp-service-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "unfc_sftp_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.unfc_sftp_child_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "unfc_sftp_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.unfc_sftp_child_resource.id
  http_method             = aws_api_gateway_method.unfc_sftp_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "unfc_sftp_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.unfc_sftp_child_resource.id
  http_method = aws_api_gateway_method.unfc_sftp_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "unfc_sftp_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.unfc_sftp_child_resource.id
  http_method = aws_api_gateway_method.unfc_sftp_options_method.http_method
  status_code = aws_api_gateway_method_response.unfc_sftp_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.unfc_sftp_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin"  : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
