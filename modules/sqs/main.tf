
resource "aws_sqs_queue" "log_processing_queue" {
  name                        = var.log_processing_queue_name
  fifo_queue                  = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    ENVIRONMENT = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

# Add SQS as Event Source for Lambda
resource "aws_lambda_event_source_mapping" "sqs_trigger" {
  event_source_arn = aws_sqs_queue.log_processing_queue.arn
  function_name    = "log-processor-${var.environment}"
  batch_size       = 1
}

# SSM Parameter for SQS URL
resource "aws_ssm_parameter" "logger_sqs_url" {
  name      = "LOGGER_SQS_URL"
  type      = "String"
  value     = aws_sqs_queue.log_processing_queue.url
  overwrite = true  # Overwrite if the parameter already exists

  tags = {
    ENVIRONMENT = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}