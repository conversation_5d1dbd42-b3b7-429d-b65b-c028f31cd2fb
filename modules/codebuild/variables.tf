variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = "Environment"
  type        = string
}

variable "environment_tag" {
  description = "Environment Tag"
  type        = string
}

//r3_oaf_frontend
variable "r3_oaf_frontend_project_name" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_build_timeout" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_source_type" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_environment_image" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_environment_type" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_project_artifact_type" {
  description = ""
  type        = string
}

//r3_oaf_backend
variable "r3_oaf_backend_project_name" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_build_timeout" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_source_type" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_environment_image" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_environment_type" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "r3_oaf_backend_project_artifact_type" {
  description = ""
  type        = string
}

//gus-middleware-service
variable "gus_middleware_service_project_name" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_source_type" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_middleware_service_project_artifact_type" {
  description = ""
  type        = string
}

//r3-pdf-generator 
variable "r3_pdf_generator_project_name" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_build_timeout" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_source_type" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_environment_image" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_environment_type" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "r3_pdf_generator_project_artifact_type" {
  description = ""
  type        = string
}

//gus-eip-analytics 
variable "gus_eip_analytics_project_name" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_source_type" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_eip_analytics_project_artifact_type" {
  description = ""
  type        = string
}

//gus-eip-logger-frontend
variable "gus_eip_logger_frontend_project_name" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_source_type" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_eip_logger_frontend_project_artifact_type" {
  description = ""
  type        = string
}

 variable "restApiId" {
  description = ""
  type = string
}

variable "restApiRootResourceId" {
  description = ""
  type = string
  
}

variable "securityGroupId" {
  description = ""
  type = string
  
}

variable "subnetId" {
  description = ""
  type = string
  
}

//gus-eip-log-processor 
variable "gus_eip_log_processor_project_name" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_source_type" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_eip_log_processor_project_artifact_type" {
  description = ""
  type        = string
}